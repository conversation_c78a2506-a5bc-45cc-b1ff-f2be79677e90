package com.oplus.recorderlog.util

import android.content.Context
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.content.pm.PackageManager.NameNotFoundException
import android.os.Build
import android.text.TextUtils
import androidx.annotation.ChecksSdkIntAtLeast
import com.oplus.recorderlog.log.RecorderLogger

object VersionUtil {

    const val TAG = "VersionUtil"

    const val ATTR_MODEL = "ro.product.name"
    private const val ATTR_OTAVERSION = "ro.build.version.ota"
    private const val ATTR_ROMVERSION = "ro.build.display.id"
    private const val ATTR_COLOROSVERSION = "ro.build.version.opporom"
    private const val ATTR_ANDROIDVERSION = "ro.build.version.release"
    private const val ATTR_UREGION = "persist.sys.oppo.region"

    var appVersionNameForLog: String? = null
    var appVersionCode: String? = null


    val otaVersion = BaseUtil.getConfigFromSystem(ATTR_OTAVERSION) ?: ""
    val romVersion = BaseUtil.getConfigFromSystem(ATTR_ROMVERSION) ?: ""
    val colorOSVersion = BaseUtil.getConfigFromSystem(ATTR_COLOROSVERSION) ?: ""
    val androidVersion = BaseUtil.getConfigFromSystem(ATTR_ANDROIDVERSION) ?: ""
    val uRegion = BaseUtil.getConfigFromSystem(ATTR_UREGION) ?: ""


    /**
     * 获取当前app version name
     * @return  13.0.1.123456_15098
     */
    @JvmStatic
    fun getAppVersionName(inputContext: Context?): String? {
        if (!TextUtils.isEmpty(appVersionNameForLog)) {
            return appVersionNameForLog
        }
        if (inputContext == null) {
            return null
        }
        var appVersionName: String? = ""
        try {
            val context = inputContext.applicationContext
            val packageInfo = context.packageManager
                .getPackageInfo(context.packageName, PackageManager.GET_META_DATA)
            val metaData = packageInfo.applicationInfo?.metaData
            if (metaData != null) {
                val versionCommit = metaData["versionCommit"]
                val versionDate = metaData["versionDate"]
                if (versionCommit != null && versionDate != null) {
                    appVersionName = StringBuilder().append(fixString(packageInfo.versionName))
                        .append("_")
                        .append(versionCommit.toString())
                        .append("_")
                        .append(versionDate.toString()).toString()
                    RecorderLogger.i(
                        BaseUtil.TAG,
                        "packageInfo.versionName->" + packageInfo.versionName, false
                    )
                    RecorderLogger.i(TAG, "versionCommit.toString()->$versionCommit", false)
                    RecorderLogger.i(TAG, "versionDate.toString()->$versionDate", false)
                    appVersionNameForLog = appVersionName
                }
            }
        } catch (e: PackageManager.NameNotFoundException) {
            RecorderLogger.e(BaseUtil.TAG, e.message, false)
        }
        return appVersionName
    }

    @JvmStatic
    fun getAppVersionCode(inputContext: Context?): CharSequence? {
        if (!TextUtils.isEmpty(appVersionCode)) {
            return appVersionCode
        }
        if (inputContext == null) {
            return null
        }
        val context = inputContext.applicationContext
        val packageManager: PackageManager = context.getPackageManager()
        try {
            val packageInfo: PackageInfo =
                packageManager.getPackageInfo(context.getPackageName(), 0)
            appVersionCode = packageInfo.longVersionCode.toString()
            return appVersionCode
        } catch (e: NameNotFoundException) {
            RecorderLogger.e(TAG, "getAppVersionCode error: $e", false)
        }
        return ""
    }

    @JvmStatic
    private fun fixString(str: String?): CharSequence? {
        return str?.replace("_", "") ?: ""
    }

    @ChecksSdkIntAtLeast(api = Build.VERSION_CODES.TIRAMISU)
    @JvmStatic
    fun isSupportGlowEffect(): Boolean {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU
    }
}