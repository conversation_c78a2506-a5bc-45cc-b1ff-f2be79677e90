/**********************************************************
 * Copyright 2010-2017 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description : FileUtils
 * History :( ID, Date, Author, Description)
 * v1.0, 2019-3-12, huang<PERSON>wang, create
 ***********************************************************/


package com.soundrecorder.base.utils;

import static com.oplus.recorderlog.log.constants.XLogDbConstant.RecorderColumn.COLUMN_NAME_DISPLAY_NAME;
import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.Context;
import android.database.Cursor;
import android.icu.text.DecimalFormat;
import android.media.MediaFormat;
import android.net.Uri;
import android.provider.MediaStore;
import android.text.TextUtils;

import com.oplus.recorderlog.util.CommonFlavor;
import com.soundrecorder.base.BaseApplication;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.channels.FileChannel;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


/***********************************************************
 * Copyright 2010-2020 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 *
 * Description    : 
 * History       :( ID, Date, Author, Description )
 * v1.0, ${date}, yinbin, create
 **********************************************************/
public class FileUtils {

    public static final String TAG = "FileUtils";


    /*回收站需求新增字段 */
    public final static String COLUMN_NAME_IS_RECYCLE = "is_recycle";
    public static final String PACKAGE_NAME = CommonFlavor.getInstance().getPackageName();
    public static final String AUTHORITY = PACKAGE_NAME + ".provider";
    public static final String CONTENT_AUTHORITY = "content://" + AUTHORITY;
    public static final String PATH_RECORD = "records";
    public static final Uri RECORD_CONTENT_URI = Uri.parse(CONTENT_AUTHORITY + "/" + PATH_RECORD);
    public static final int SECONDS_1000 = 1000;
    public static final int MAX_FILE_NAME_LENGTH = 50;
    private static final String TABLE_NAME_PICTURE_NAME = "picture_mark";
    private static final String MIMETYPE_KEY = "mimeType";

    private static final long GB = 1024 * 1024 * 1024;
    private static final long MB = 1024 * 1024;
    private static final long KB = 1024;
    private static final String GBSTRING = "GB";
    private static final String MBSTRING = "MB";
    private static final String KBSTRING = "KB";
    private static final String BSTRING = "B";



    public static boolean isFileExist(String relativePath, String displayName) {
        Uri queryUri = getMediaUriByRelativePath(BaseApplication.getAppContext(), relativePath, displayName);
        return isFileExist(queryUri);
    }

    public static boolean isFileExist(Uri uri) {
        boolean result = false;
        InputStream inputStream = null;
        if (uri == null) {
            return result;
        }
        try {
            inputStream = BaseApplication.getAppContext().getContentResolver().openInputStream(uri);
            result = true;
        } catch (FileNotFoundException fne) {
            DebugUtil.e(TAG, "isFileExist no file found: uri: " + uri);
            result = false;
        } catch (SecurityException e) {
            DebugUtil.e(TAG, "isFileExist no security exception: uri: " + uri);
            result = false;
        } catch (Exception e) {
            DebugUtil.e(TAG, "isFileExist other exception: uri: " + uri, e);
            result = false;
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                DebugUtil.e(TAG, "isFileExist close pfd ", e);
            }
        }
        return result;
    }

    private static boolean isRecycleFileExist(String displayName) {
        boolean isExist = false;
        Cursor cursor = null;
        try {
            String[] projections = {COLUMN_NAME_DISPLAY_NAME, COLUMN_NAME_IS_RECYCLE};
            String selection = COLUMN_NAME_DISPLAY_NAME + " = ? AND " + COLUMN_NAME_IS_RECYCLE + " = 1";
            String[] args = {displayName};
            ContentResolver resolver = BaseApplication.getAppContext().getContentResolver();
            cursor = resolver.query(RECORD_CONTENT_URI, projections, selection, args, null);
            if (cursor != null && cursor.getCount() > 0) {
                isExist = true;
                DebugUtil.e(TAG, "isRecycleFileExist cursor " + cursor.getCount());
            }

        } catch (Exception e) {
            DebugUtil.e(TAG, "isRecycleFileExist error, e = " + e.getMessage());
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        DebugUtil.e(TAG, "isRecycleFileExist isExist =  " + isExist);
        return isExist;
    }


    public static boolean checkFileNotExistOrFileSizeZero(String path, Uri uri) {
        boolean isFileExists = false;
        long fileSize = 0;
        if (BaseUtil.isAndroidQOrLater()) {
            isFileExists = isFileExist(uri);
            fileSize = getFileSize(uri);
        } else {
            File file = new File(path);
            isFileExists = file.exists();
            fileSize = file.length();
        }
        if (!isFileExists || (fileSize == 0)) {
            DebugUtil.i(TAG,
                    "checkFileNotExistOrFileSizeZero input name file not exist or file length == 0 , return : " + getDisplayNameByPath(path));
            return true;
        }
        return false;
    }

    public static String getSuffix(String displayName) {
        String suffix = "";
        if (!TextUtils.isEmpty(displayName)) {
            int lastDot = displayName.lastIndexOf(".");
            if (lastDot > 0) {
                suffix = displayName.substring(lastDot);
            }
        }
        return suffix;
    }

    public static String getFileNameNoEx(String filename) {
        if ((filename != null) && (filename.length() > 0)) {
            int dot = filename.lastIndexOf('.');
            if ((dot > -1) && (dot < filename.length())) {
                return filename.substring(0, dot);
            }
        }
        return filename;
    }

    public static long getRealFileSize(String relativePath, String displayName) {
        long fileSize = -1;
        Context context = BaseApplication.getAppContext();
        Uri queryUri = getMediaUriByRelativePath(context, relativePath, displayName);
        if (queryUri != null) {
            fileSize = getFileSize(queryUri);
        } else {
            DebugUtil.i(TAG, "getRealFileSize: no record found , relativePath: " + relativePath + ", displayName: " + displayName);
        }
        return fileSize;
    }

    public static long getFileSize(Uri uri) {
        long size = -1;
        FileInputStream stream = null;
        FileChannel channel = null;
        try {
            stream = (FileInputStream) BaseApplication.getAppContext().getContentResolver().openInputStream(uri);
            if (stream != null) {
                channel = stream.getChannel();
                size = channel.size();
            }
        } catch (FileNotFoundException e) {
            DebugUtil.e(TAG, "getFileSize get file size file not found, uri: " + uri);
        } catch (Exception e) {
            DebugUtil.e(TAG, "getFileSize get file size error", e);
        } finally {
            try {
                if (channel != null) {
                    channel.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                if (stream != null) {
                    stream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return size;
    }

    public static long getFileSizeLong(String filePath) {
        if (TextUtils.isEmpty(filePath)) {
            return -1;
        }
        File file = new File(filePath);
        if (!file.exists()) {
            return -1;
        }
        long size = file.length();
        return size;
    }

    public static Uri getMediaUriByRelativePath(Context context, String relativePath, String displayName) {
        String[] projection = {MediaStore.MediaColumns._ID, MediaStore.MediaColumns.DISPLAY_NAME,
                MediaStore.MediaColumns.SIZE};
        Cursor cursor = null;
        long mediaId = -1;
        Uri queryUri = null;
        try {
            cursor = context.getContentResolver().query(
                    MediaStore.Audio.Media.EXTERNAL_CONTENT_URI,
                    projection,
                    MediaStore.MediaColumns.RELATIVE_PATH + " COLLATE NOCASE = ? AND " + MediaStore.MediaColumns.DISPLAY_NAME + " COLLATE NOCASE = ? AND "
                            + MediaStore.MediaColumns.SIZE + " > ? ",
                    new String[]{relativePath, displayName, String.valueOf(0)},
                    null);
            if ((cursor != null) && cursor.moveToFirst()) {
                int idIndex = cursor.getColumnIndex(MediaStore.MediaColumns._ID);
                if (idIndex >= 0) {
                    mediaId = cursor.getLong(idIndex);
                }
            }
            if (mediaId != -1) {
                queryUri = ContentUris.withAppendedId(MediaStore.Audio.Media.EXTERNAL_CONTENT_URI, mediaId);
            }
            DebugUtil.i(TAG, "getMediaUriByRelativePath: uri: " + queryUri);
        } catch (Exception e) {
            DebugUtil.e(TAG, "getMediaUriByRelativePath isFileExist query mediaStore: ", e);
        } finally {
            if (cursor != null) {
                cursor.close();
            }
        }
        return queryUri;
    }

    public static String getNewDisplayName(String relativePath, String displayName) {
        return getNewDisplayName(relativePath, displayName, "");
    }

    public static String getNewDisplayName(String relativePath, String displayName, String defaultNameSuffix) {
        int i = 1;
        String title = "";
        String suffix = "";
        int lastDot = displayName.lastIndexOf(".");
        if (lastDot <= 0) {
            title = displayName;
        } else {
            suffix = displayName.substring(lastDot);
            title = displayName.substring(0, lastDot);
        }

        String joiner = "_";
        String newName = "";
        // 有连接符
        if (!TextUtils.isEmpty(defaultNameSuffix)) {
            i = 0;
            title = calRequireLengthTitle(title, defaultNameSuffix, joiner);
            newName = title + suffix;
        } else {
            newName = calRequireLengthTitle(title, String.valueOf(i), joiner) + suffix;
        }

        while (isFileExist(relativePath, newName)) {
            i++;
            newName = calRequireLengthTitle(title, String.valueOf(i), joiner) + suffix;
        }
        return newName;
    }

    /**
     * 回收站文件名冲突
     * @param displayName
     * @param defaultNameSuffix
     * @return
     */
    public static String getNewRecycleDisplayName(String displayName, String defaultNameSuffix) {
        int i = 1;
        String title = "";
        String suffix = "";
        int lastDot = displayName.lastIndexOf(".");
        if (lastDot <= 0) {
            title = displayName;
        } else {
            suffix = displayName.substring(lastDot);
            title = displayName.substring(0, lastDot);
        }

        String joiner = "_";
        String newName = "";
        // 有连接符
        title = calRequireLengthTitle(title, defaultNameSuffix, joiner);
        newName = title + suffix;
        return newName;
    }


    public static String calRequireLengthTitle(String originTitle, String titleSuffix, String joiner) {
        String newTitle = originTitle + joiner + titleSuffix;
        if (newTitle.length() < MAX_FILE_NAME_LENGTH) {
            return newTitle;
        }
        int overLength = newTitle.length() - MAX_FILE_NAME_LENGTH;
        int endIndex = originTitle.length() - overLength;

        return originTitle.substring(0, endIndex) + joiner + titleSuffix;
    }

    public static String getMimeType(String extraInfo) {
        String mimeType = MediaFormat.MIMETYPE_AUDIO_MPEG;
        if (!TextUtils.isEmpty(extraInfo)) {
            try {
                JSONObject json = new JSONObject(extraInfo);
                mimeType = json.getString(MIMETYPE_KEY);
            } catch (JSONException e) {
                DebugUtil.e(TAG, "getMimeType extraInfo from cloud is not json object. ", e);
            }
        }
        return mimeType;
    }

    public static void ensureDirectory(String directory) {
        if (!TextUtils.isEmpty(directory)) {
            File file = new File(directory);
            if (file.exists()) {
                if (!file.isDirectory()) {
                    DebugUtil.d(TAG, " ensureDirectory " + getDisplayNameByPath(directory) + " is actually a normal file! ");
                    File backupFile = new File(directory + System.currentTimeMillis());
                    boolean success = file.renameTo(backupFile);
                    if (success) {
                        boolean ans = file.mkdirs();
                        DebugUtil.d(TAG, "ensureDirectory after rename file.mkdirs() ans;" + ans);
                    } else {
                        DebugUtil.d("RecorderUtil", " ensureDirectory rename failed! ");
                        if (file.delete()) {
                            boolean ans = file.mkdirs();
                            DebugUtil.d(TAG, "ensureDirectory after delete file.mkdirs() ans;" + ans);
                        } else {
                            DebugUtil.d(TAG, "ensureDirectory delete failed! ");
                        }
                    }
                }
            } else {
                boolean ans = file.mkdirs();
                DebugUtil.d(TAG, "ensureDirectory  not exist file.mkdirs() ans;" + ans);
            }
        }
    }

    public static String getDisplayNameByPath(String inputPath) {
        String displayName = "";
        if (TextUtils.isEmpty(inputPath)) {
            return displayName;
        }
        int lastIndexOfFileSeparator = inputPath.lastIndexOf(File.separator);
        if (lastIndexOfFileSeparator > 0 && lastIndexOfFileSeparator + 1 <= inputPath.length()) {
            displayName = inputPath.substring(lastIndexOfFileSeparator + 1);
        }
        return displayName;
    }

    public static String getFileNameFromFullPath(String fullPath) {
        String result = "";
        if (!TextUtils.isEmpty(fullPath)) {
            int index = fullPath.lastIndexOf(File.separator);
            if ((index > 0) && (index < (fullPath.length() - 1))) {
                result = fullPath.substring(index + 1);
            }
        }
        return result;
    }

    public static File getAppFile() {
        return getAppFile("", true);
    }

    /**
     * @param fileName
     * @param shouldCreate default true
     * @return
     */
    public static File getAppFile(String fileName, boolean shouldCreate) {
        File file;
        if (fileName.isEmpty()) {
            file = new File(BaseApplication.getAppContext().getFilesDir() + File.separator + TABLE_NAME_PICTURE_NAME, "IMG_SOUNDER_" + UUID.randomUUID() + ".jpg");
        } else {
            file = new File(BaseApplication.getAppContext().getFilesDir() + File.separator + TABLE_NAME_PICTURE_NAME, fileName);
        }
        DebugUtil.i(TAG, "getAppFile com.soundrecorder.base.utils" + file);
        if (shouldCreate && !file.exists()) {
            createOrExistsFile(file);
        }
        return file;
    }

    private static boolean createOrExistsFile(File file) {
        if (file == null) return false;
        if (file.exists()) return file.isFile();
        if (!createOrExistsDir(file.getParentFile())) {
            return false;
        } else {
            try {
                return file.createNewFile();
            } catch (IOException e) {
                DebugUtil.e(TAG, "createOrExistsFile createNewFile e=$e");
                return false;
            }
        }
    }

    private static boolean createOrExistsDir(File file) {
        if (file != null) {
            return file.exists() ? file.isDirectory() : file.mkdirs();
        }
        return false;
    }

    public static boolean copyFile(Context context, Uri src, Uri dest) {
        boolean result = false;
        if ((src == null) || (context == null) || (dest == null)) {
            return false;
        }
        FileInputStream srcInputStream = null;
        FileOutputStream destOutStream = null;
        FileChannel srcChannel = null;
        FileChannel destChannel = null;
        try {
            srcInputStream = (FileInputStream) context.getContentResolver().openInputStream(src);
            destOutStream = (FileOutputStream) context.getContentResolver().openOutputStream(dest);
            if (srcInputStream != null) {
                srcChannel = srcInputStream.getChannel();
                if (destOutStream != null) {
                    destChannel = destOutStream.getChannel();
                    destChannel.transferFrom(srcChannel, 0, srcChannel.size());
                    result = true;
                }
            }
        } catch (FileNotFoundException e) {
            DebugUtil.e(TAG, "copyFile copy file not found error:", e);
            result = false;
        } catch (IOException e) {
            DebugUtil.e(TAG, "copyFile copy file io error:", e);
            result = false;
        } catch (NullPointerException e) {
            DebugUtil.e(TAG, "copyFile copy file io error:", e);
            result = false;
        } finally {
            try {
                if (srcInputStream != null) {
                    srcInputStream.close();
                }
                if (destOutStream != null) {
                    destOutStream.close();
                }
                if (srcChannel != null) {
                    srcChannel.close();
                }
                if (destChannel != null) {
                    destChannel.close();
                }
            } catch (IOException e) {
                DebugUtil.e(TAG, "copyFile close file io error:", e);
            }
        }
        return result;
    }

    public static File copyFile(File src, String destPath, String displayName) {
        if ((src == null) || !src.exists()) {
            return null;
        }
        DebugUtil.i(TAG, "copyFile srcfile: " + src + ", destName: " + getDisplayNameByPath(destPath) + ", displayName: " + displayName);
        ensureDirectory(destPath);
        return copyFileWithOutCreateFolder(src, destPath, displayName);
    }

    public static File copyFileWithOutCreateFolder(File src, String destPath, String displayName) {
        File dest = FileRenameUtil.create(destPath, displayName);
        DebugUtil.i(TAG, "copyFileWithOutCreateFolder create file dest " + dest);
        if (dest == null) {
            DebugUtil.i(TAG, "copyFileWithOutCreateFolder create file failed: "
                    + src + ", destName: " + getDisplayNameByPath(destPath) + ", displayName: " + displayName);
            return null;
        }
        try {
            boolean createResult = dest.createNewFile();
            if (!createResult) {
                DebugUtil.e(TAG, "copyFileWithOutCreateFolder create dest file failed:" + dest.getName());
            }
        } catch (IOException e) {
            DebugUtil.e(TAG, "copyFileWithOutCreateFolder copy file create dest file error:", e);
        }
        FileInputStream srcInputStream = null;
        FileOutputStream dstOutStream = null;
        FileChannel srcChannel = null;
        FileChannel dstChannel = null;
        try {
            srcInputStream = new FileInputStream(src);
            dstOutStream = new FileOutputStream(dest);
            srcChannel = srcInputStream.getChannel();
            dstChannel = dstOutStream.getChannel();
            dstChannel.transferFrom(srcChannel, 0, srcChannel.size());
        } catch (FileNotFoundException fileNotFoundException) {
            DebugUtil.e(TAG, "copyFileWithOutCreateFolder copy file not found error:", fileNotFoundException);
        } catch (IOException ioException) {
            DebugUtil.e(TAG, "copyFileWithOutCreateFolder copy file io error:", ioException);
        } finally {
            try {
                if (srcInputStream != null) {
                    srcInputStream.close();
                }
                if (dstOutStream != null) {
                    dstOutStream.close();
                }
                if (srcChannel != null) {
                    srcChannel.close();
                }
                if (dstChannel != null) {
                    dstChannel.close();
                }
            } catch (IOException ioException) {
                DebugUtil.e(TAG, "copyFileWithOutCreateFolder close file io error:", ioException);
            }
        }
        return dest;
    }

    public static ArrayList<String> deleteFileList(List<String> deleteList) {
        ArrayList<String> realDeleteList = new ArrayList<String>();
        if ((deleteList == null) || deleteList.isEmpty()) {
            DebugUtil.i(TAG, "deleteFileList, deleteList is empty!");
            return realDeleteList;
        }
        for (String filePath : deleteList) {
            try {
                File file = new File(filePath);
                DebugUtil.i(TAG, "deleteFileList, name=" + getDisplayNameByPath(filePath) + ", exists=" + file.exists() + ", isFile=" + file.isFile());
                if (file.exists() && file.isFile()) {
                    if (file.delete()) {
                        realDeleteList.add(filePath);
                    } else {
                        DebugUtil.i(TAG, "deleteFileList, delete file failed: " + getDisplayNameByPath(filePath));
                    }
                } else {
                    DebugUtil.i(TAG, "deleteFileList, delete file not exist or not a file: " + getDisplayNameByPath(filePath));
                }
            } catch (Exception e) {
                DebugUtil.e(TAG, "deleteFileList, delete file exception: " + getDisplayNameByPath(filePath) + ",e:" + e.getMessage());
            }
        }
        return realDeleteList;
    }

    public static boolean modifyFileLastModifyTimeByShell(String filePath, long modifyTime) {
        File file = new File(filePath);
        DebugUtil.i(TAG, "modifyFileLastModifyTimeByShell before modifyFileLastModifyTimeByShell: name: "
                + getDisplayNameByPath(filePath) + ", before time: " + file.lastModified());
        boolean modifiedSuc = file.setLastModified(modifyTime * SECONDS_1000);
        DebugUtil.i(TAG, "modifyFileLastModifyTimeByShell: name: " + getDisplayNameByPath(filePath)
                + ", modifyTime: " + modifyTime + ", modifiedSuc: " + modifiedSuc + ", after time: " + file.lastModified());
        return modifiedSuc;
    }

    public static String getSizeDescription(long size) {
        StringBuilder bytes = new StringBuilder();
        DecimalFormat format = new DecimalFormat("#");
        if (size >= GB) {
            double i = (size >> NumberConstant.NUM_30);
            bytes.append(format.format(i)).append(GBSTRING);
        } else if (size >= MB) {
            double i = (size >> NumberConstant.NUM_20);
            bytes.append(format.format(i)).append(MBSTRING);
        } else if (size >= KB) {
            double i = (size >> NumberConstant.NUM_10);
            bytes.append(format.format(i)).append(KBSTRING);
        } else {
            if (size <= 0) {
                bytes.append("0");
                bytes.append(BSTRING);
            } else {
                bytes.append((int) size).append(BSTRING);
            }
        }
        return bytes.toString();
    }

    public static String core2Full(String coreName, String fullName) {
        File theFile = new File(fullName);
        if ((fullName.length() - NumberConstant.NUM_4) > 0) {
            return theFile.getParent() + File.separator + coreName
                    + fullName.substring(fullName.length() - NumberConstant.NUM_4);
        }
        return theFile.getParent() + File.separator + coreName;
    }

    public static String core2FullWithEdit(String coreName, String fullName) {
        File theFile = new File(fullName);
        return theFile.getParent() + File.separator + coreName;
    }

    public static void deleteDirOrFile(Context context, File rootFile) {
        if ((rootFile == null) || (!rootFile.exists())) {
            return;
        }
        if (rootFile.isDirectory()) {
            File[] files = rootFile.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.exists()) {
                        if (file.isDirectory()) {
                            deleteDirOrFile(context, file);
                        } else {
                            delete(context, file);
                        }
                    }
                }
            }
        }
        delete(context, rootFile);
    }

    protected static boolean delete(Context context, File file) {
        if (file == null) {
            return false;
        }
        if (!file.exists()) {
            return false;
        }
        return file.delete();
    }
}
