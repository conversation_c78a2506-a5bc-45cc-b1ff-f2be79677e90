apply from: "../../common_build.gradle"

android {
    sourceSets {
        main {
            res.srcDirs += ['res']
            res.srcDirs += ['../../res-strings']
            aidl.srcDirs += ['../aidl']
        }
    }
    namespace "com.soundrecorder.common"
    buildFeatures {
        aidl true
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation libs.org.kotlin.stdlib
    implementation libs.androidx.appcompat
    implementation libs.androidx.core.ktx

    // base包为必须引用的包，prop_versionName需保持一致
    implementation (libs.oplus.coui.core) {
        exclude group: 'com.squareup.okio', module: 'okio'
    }
    // 以下子包应用可选使用，如有使用了如下子包的控件，则需要添加，未使用可以不引用
    implementation libs.oplus.coui.dialog
    implementation libs.oplus.coui.poplist
    implementation libs.oplus.coui.snackbar
    implementation libs.oplus.coui.recyclerview
    implementation libs.oplus.coui.tips
    implementation libs.oplus.coui.preference
    implementation libs.oplus.coui.scrollbar
    implementation libs.oplus.coui.progressbar
    implementation libs.oplus.coui.toolbar
    implementation libs.oplus.coui.panel
    implementation libs.oplus.coui.scrollview
    implementation libs.oplus.coui.statement
    implementation libs.oplus.coui.seekbar
    implementation libs.oplus.coui.input
    implementation libs.oplus.coui.cardview
    implementation libs.androidx.asynclayoutinflater
    implementation libs.oplus.aiunit.core
    implementation(libs.oplus.aiunit.toolkit)
    implementation(libs.oplus.aiunit.download)
    implementation(libs.oplus.aiunit.common)
    testImplementation libs.oplus.coui.core
    testImplementation libs.oplus.coui.panel
    testImplementation libs.oplus.coui.dialog
    testImplementation libs.oplus.coui.snackbar
    testImplementation libs.oplus.coui.recyclerview
    testImplementation libs.oplus.coui.tips
    testImplementation libs.oplus.coui.preference
    testImplementation libs.oplus.coui.scrollbar
    testImplementation libs.oplus.coui.progressbar
    testImplementation libs.oplus.coui.toolbar

    compileOnly libs.oplus.addon
    testImplementation libs.oplus.addon

    implementation libs.gson

    // Koin for Android
    implementation(libs.koin)

    implementation project(':common:libbase')
    implementation project(':common:RecorderLogBase')
    implementation project(':common:libimageload')
    implementation project(':common:modulerouter')
    implementation libs.oplus.vfxsdk.coecommon
    implementation libs.oplus.vfxsdk.rsview
}
