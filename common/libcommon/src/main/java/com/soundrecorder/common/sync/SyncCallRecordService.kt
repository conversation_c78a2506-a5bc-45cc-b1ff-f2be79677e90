/************************************************************
 * Copyright 2000-2020 OPPO Mobile Comm Corp., Ltd.
 * All rights reserved.
 * FileName      : SyncCallRecordService.kt
 * Version Number: 1.0
 * Description   :
 * Author        : tianjun
 * Date          : 2020.06.30
 * History       :(ID,  2020.06.30, tianjun, Description)
 ************************************************************/
package com.soundrecorder.common.sync

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.core.app.JobIntentService
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.db.RecorderDBUtil
import com.soundrecorder.modulerouter.cloudkit.CloudKitInterface
import com.soundrecorder.modulerouter.waveMark.WaveMarkInterface
import com.soundrecorder.modulerouter.utils.Injector

/**
 * @Deprecated
 * 通话录音完成后通知录音及时同步通话录音到录音+云同步
 */
class SyncCallRecordService : JobIntentService() {

    private val cloudKitApi by lazy {
        Injector.injectFactory<CloudKitInterface>()
    }

    private val waveMarkApi by lazy {
        Injector.injectFactory<WaveMarkInterface>()
    }

    override fun onHandleWork(intent: Intent) {
        doSync(applicationContext, intent)
    }

    private fun doSync(context: Context?, intent: Intent?) {
        DebugUtil.i(TAG, intent?.toString())
        intent?.run {
            if ((context != null) && (action == CALL_RECORD_COMPLETED)) {
                //1. Receive the broadcast of call recording and get the URI of call recording
                var uris: ArrayList<Uri>? = null
                try {
                    uris = getParcelableArrayListExtra<Uri>(Intent.EXTRA_STREAM)
                } catch (e: Exception) {
                    DebugUtil.e(TAG, "getParcelableArrayListExtra error.", e)
                }
                if (uris == null || uris.isEmpty()) {
                    uris = ArrayList<Uri>()
                    data?.let {
                        uris.add(it)
                    }
                }
                val uriCount = uris.size
                if (uriCount <= 0) {
                    DebugUtil.e(TAG, "receive call recording uris is empty.")
                } else {
                    var failedCount = 0
                    val syncSuccessUris = ArrayList<Uri>()
                    uris.forEach {
                        //2. Query the corresponding call recording information from the media library
                        //3. Synchronize call recording information to recording database
                        val result = RecorderDBUtil.getInstance(context).insertOrUpdateCallRecord(it, null)
                        if (!result) {
                            DebugUtil.e(TAG, "insert call recording to recorder db failed. uri = $it")
                            failedCount++
                        } else {
                            syncSuccessUris.add(it)
                        }
                    }
                    //4. Trigger recording cloud synchronization
                    if (uriCount > failedCount) {
                        DebugUtil.i(TAG, "trig sync call recording from broadcast.")
                        cloudKitApi?.trigBackupNow(context)
                        //5.decode call recording amplitude in recorder db
                        doDecodeAmplitude(syncSuccessUris)
                    } else {
                        DebugUtil.e(TAG, "all uris insert or update to recorder db failed. uris = $uris")
                    }
                }
            }
        }
    }

    private fun doDecodeAmplitude(uris: ArrayList<Uri>) {
        uris.forEach {
            RecorderDBUtil.getInstance(applicationContext).insertOrUpdateCallRecord(it, waveMarkApi?.decodeAmplitudeByUri(it))
        }
    }

    companion object {
        const val CALL_RECORD_COMPLETED = "coloros.intent.action.CALL_RECORD_COMPLETED"
        const val TAG = "SyncCallRecordService"
        const val JOB_ID = 0x168
    }
}