/*********************************************************************
 ** Copyright (C), 2010-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : AISettingsExt.kt
 ** Description : AISettingsExt.kt
 ** Version     : 1.0
 ** Date        : 2025/07/29
 ** Author      : <PERSON><PERSON><PERSON>.<EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  Jiafei.Liu     2025/07/29     1.0      create
 ***********************************************************************/
package com.soundrecorder.common.utils

import android.content.Context
import com.oplus.aiunit.core.data.DetectData
import com.oplus.aiunit.toolkits.AISettings
import com.soundrecorder.base.utils.DebugUtil

private const val TAG = "AISettingsExt"
private const val RETRY_TIME = 3
private const val RETRY_INTERVAL = 100L
private const val DETECT_DATA_MIN_SIZE = 2

fun AISettings.getSafeDetectData(context: Context, detectName: String): DetectData? {
    DebugUtil.i(TAG, "getSafeDetectData detectName: $detectName")
    var retryCount = RETRY_TIME
    var lastDetectData: DetectData? = null
    while (retryCount > 0) {
        val detectData = runCatching { getDetectData(context, detectName) }.onFailure {
            DebugUtil.e(TAG, "getSafeDetectData $retryCount, failed: ${it.message}")
        }.getOrNull()
        DebugUtil.i(TAG, "getSafeDetectData detectData: ${detectData?.getData()}")
        if (detectData != null && detectData.getData().size() >= DETECT_DATA_MIN_SIZE) {
            return detectData
        }
        DebugUtil.i(TAG, "getSafeDetectData retryCount: $retryCount")
        lastDetectData = detectData
        retryCount--
        if (retryCount > 0) {
            Thread.sleep(RETRY_INTERVAL)
        }
    }
    return lastDetectData.apply {
        DebugUtil.i(TAG, "getSafeDetectData $lastDetectData")
    }
}

