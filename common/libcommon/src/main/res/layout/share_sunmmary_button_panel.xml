<?xml version="1.0" encoding="utf-8"?>
<com.coui.appcompat.view.COUILinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:orientation="vertical"
    android:layout_height="wrap_content">

    <com.coui.appcompat.button.COUIButton
        android:id="@+id/dialog_share_button"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp44"
        android:layout_marginStart="@dimen/dp32"
        android:layout_marginEnd="@dimen/dp32"
        android:layout_marginTop="@dimen/dp16"
        android:layout_marginBottom="@dimen/dp24"
        android:background="@drawable/rounded_button_background"
        android:gravity="center_horizontal|center_vertical"
        android:text="@string/share"
        android:textColor="#FFFFFF"
        android:textSize="@dimen/sp16"
        tools:ignore="MissingConstraints" />

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/dp52"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />
</com.coui.appcompat.view.COUILinearLayout>