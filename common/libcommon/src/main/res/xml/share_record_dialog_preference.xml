<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <com.coui.appcompat.preference.COUIPreferenceCategory >
        <com.coui.appcompat.preference.COUIJumpPreference
            android:defaultValue="false"
            android:icon="@drawable/share_record_link"
            android:key="share_record_share_link"
            android:persistent="false"
            android:title="@string/share_link"
            app:couiIsCustomIcon="true"
            app:couiShowDivider="true"
            app:coui_jump_mark="@null" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>
    <com.coui.appcompat.preference.COUIPreferenceCategory >
        <com.coui.appcompat.preference.COUIJumpPreference
            android:defaultValue="true"
            android:icon="@drawable/share_record_audio"
            android:key="share_record_share_audio"
            android:persistent="false"
            android:title="@string/share_audio"
            app:couiIsCustomIcon="true"
            app:couiShowDivider="true"
            app:coui_jump_mark="@null" />
        <com.coui.appcompat.preference.COUIJumpPreference
            android:defaultValue="false"
            android:icon="@drawable/share_record_text"
            android:key="share_record_share_text"
            android:persistent="false"
            android:title="@string/share_asr"
            app:couiIsCustomIcon="true"
            app:couiShowDivider="true" />
        <com.coui.appcompat.preference.COUIJumpPreference
            android:defaultValue="false"
            android:icon="@drawable/share_record_summary"
            android:key="share_record_share_summary"
            android:persistent="false"
            android:title="@string/share_summary"
            app:couiIsCustomIcon="true"
            app:couiShowDivider="true" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>
    <com.coui.appcompat.preference.COUIPreferenceCategory>
        <com.coui.appcompat.preference.COUIJumpPreference
            android:layout="@layout/padding_view"
            android:selectable="false" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>
</PreferenceScreen>