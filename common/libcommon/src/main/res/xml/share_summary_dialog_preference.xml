<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="374dp">
    <com.coui.appcompat.preference.COUIPreferenceCategory>
        <com.coui.appcompat.preference.COUIJumpPreference
            android:defaultValue="false"
            android:key="share_summary_word_type"
            android:persistent="false"
            app:couiIconStyle="round"
            app:couiIsCustomIcon="true"
            app:couiShowDivider="true" />
        <com.coui.appcompat.preference.COUIJumpPreference
            android:defaultValue="true"
            android:key="share_summary_txt_type"
            android:persistent="false"
            app:couiIsCustomIcon="true"
            app:couiShowDivider="true" />
        <com.coui.appcompat.preference.COUIJumpPreference
            android:defaultValue="false"
            android:key="share_summary_pdf_type"
            android:persistent="false"
            app:couiIsCustomIcon="true"
            app:couiShowDivider="true" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>
    <com.coui.appcompat.preference.COUIPreferenceCategory >
        <com.coui.appcompat.preference.COUIJumpPreference
            android:defaultValue="true"
            android:key="share_summary_note_type"
            android:persistent="false"
            android:title="@string/summary_export_to_note"
            app:couiIsCustomIcon="true"
            app:couiShowDivider="true"
            app:coui_jump_mark="@null"/>
    </com.coui.appcompat.preference.COUIPreferenceCategory>
    <com.coui.appcompat.preference.COUIPreferenceCategory>
        <com.coui.appcompat.preference.COUIJumpPreference
            android:layout="@layout/padding_share_view"
            android:selectable="false" />
    </com.coui.appcompat.preference.COUIPreferenceCategory>
</PreferenceScreen>