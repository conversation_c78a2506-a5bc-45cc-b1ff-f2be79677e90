/*********************************************************************
 * * Copyright (C), 2022, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  PrivacyPolicyInfoManager
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/10/11
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.privacypolicy

import android.text.SpannableStringBuilder
import android.text.Spanned
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AppCompatDialog
import androidx.core.content.ContextCompat
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.statement.COUIStatementClickableSpan
import com.coui.appcompat.statement.COUIUserStatementDialog
import com.coui.appcompat.statement.COUIUserStatementDialog.COUIUserStatementListItem
import com.soundrecorder.base.ext.dismissWhenShowing
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.base.utils.ClickUtils
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.base.PrivacyPolicyBaseActivity.Companion.syncPrivacyDialogShowingType
import com.soundrecorder.common.dialog.setOnBackPressedListener
import com.soundrecorder.common.permission.PermissionUtils
import com.soundrecorder.common.permission.PermissionUtils.setAllFuncTypePermission
import com.soundrecorder.common.utils.CoroutineUtils.delayInMain
import com.soundrecorder.modulerouter.SettingInterface
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyDelegate
import com.soundrecorder.modulerouter.privacyPolicy.IPrivacyPolicyResultListener
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_PERMISSION_AI_PRIVACY_GUIDE
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_PERMISSION_CLOUD
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_PERMISSION_FEEDBACK
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_PERMISSION_SMART_SHORTHAND
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_PERMISSION_SUMMARY_MIND
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_FUNCTION
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_PERMISSION_USER_NOTICE_UPDATE
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_USER_NOTICE
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_USER_NOTICE_BASIC_STILL
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_USER_NOTICE_DEFAULT
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.TYPE_USER_NOTICE_LIGHT_OS
import com.soundrecorder.modulerouter.privacyPolicy.PrivacyPolicyConstant.isRemoveStatementMaskView
import com.soundrecorder.modulerouter.smartname.SmartNameAction
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.summary.util.AIUnitApi

open class PrivacyPolicyDialogManager(private val context: AppCompatActivity, private val resultCallback: IPrivacyPolicyResultListener?) {
    companion object {
        private const val TAG = "PrivacyPolicyDialogManager"
        private const val DELAY_TIME = 500L

        @JvmStatic
        fun newInstance(
            type: Int = IPrivacyPolicyDelegate.POLICY_TYPE_COMMON,
            context: AppCompatActivity,
            resultListener: IPrivacyPolicyResultListener?
        ): PrivacyPolicyDialogManager {
            return when (type) {
                IPrivacyPolicyDelegate.POLICY_TYPE_COMMON -> PrivacyPolicyDialogManager(context, resultListener)
                else -> PrivacyPolicyDialogManager(context, resultListener)
            }
        }
    }

    private val smartNameAction by lazy {
        Injector.injectFactory<SmartNameAction>()
    }

    private val settingApi by lazy {
        Injector.injectFactory<SettingInterface>()
    }

    private val dialogMap = mutableMapOf<Int, AppCompatDialog>()

    /*记录当前最后一次显示的用户弹窗Type*/
    private var currentShowingDialogType: Int? = null
    private val showDialogKeys = arrayListOf<Int>()
    private val statementView by lazy {
        View(context).apply {
            setBackgroundColor(
                ContextCompat.getColor(
                    context,
                    com.support.appcompat.R.color.coui_color_white
                )
            )
        }
    }
    private var isReCreate = false
    var hasConvertPermission = false
    private var disableDialog: AlertDialog? = null

    private fun addStatementView(pageFrom: Int?) {
        if (isRemoveStatementMaskView(pageFrom)) return

        if (statementView.parent == null) {
            context.addContentView(
                statementView, ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
            )
        }
    }

    private fun removeStatementView() {
        (statementView.parent as? ViewGroup)?.removeView(statementView)
    }

    @Suppress("LongParameterList")
    protected open fun createUserNoticeDialog(
        title: String,
        message: CharSequence,
        protocol: CharSequence,
        ok: String,
        cancel: String,
        clickOk: () -> Unit,
        clickCancel: () -> Unit,
        clickBack: () -> Unit,
    ): COUIBottomSheetDialog {
        val statementDialogType = arrayListOf(TYPE_USER_NOTICE_DEFAULT, TYPE_USER_NOTICE, TYPE_PERMISSION_USER_NOTICE_UPDATE)
        val statementListItem = if (currentShowingDialogType in statementDialogType) {
            createStatementItems()
        } else {
            null
        }
        val dialog = COUIUserStatementDialog(context).apply {
            titleText = title
            bottomButtonText = ok
            exitButtonText = cancel
            statement = message
            protocolText = protocol
            listItems = statementListItem
            onButtonClickListener = object : COUIUserStatementDialog.OnButtonClickListener {
                override fun onBottomButtonClick() {
                    clickOk.invoke()
                }

                override fun onExitButtonClick() {
                    clickCancel.invoke()
                }
            }
            setOnCancelListener { clickBack.invoke() }
            setOnBackPressedListener(clickBack)
        }

        dialog.logoDrawable = context.getDrawable(com.soundrecorder.common.R.drawable.ic_user_notice)
        dialog.appMessage = context.resources.getString(com.soundrecorder.common.R.string.audio_recording_playback)
        dialog.appName = context.getString(com.soundrecorder.common.R.string.app_name_main)
        return dialog
    }

    private fun createStatementItems(): MutableList<COUIUserStatementListItem> {
        return mutableListOf<COUIUserStatementListItem>().apply {
            add(
                COUIUserStatementListItem(
                    ContextCompat.getDrawable(context, R.drawable.intro_convert_text),
                    context.resources.getString(com.soundrecorder.common.R.string.intro_convert_text_title),
                    context.resources.getString(com.soundrecorder.common.R.string.intro_convert_text_desc)
                )
            )
            add(
                COUIUserStatementListItem(
                    ContextCompat.getDrawable(context, R.drawable.intro_record_assistant),
                    context.resources.getString(com.soundrecorder.common.R.string.intro_record_assistant_title),
                    context.resources.getString(com.soundrecorder.common.R.string.intro_record_assistant_desc)
                )
            )
            add(
                COUIUserStatementListItem(
                    ContextCompat.getDrawable(context, R.drawable.intro_text_summary),
                    context.resources.getString(com.soundrecorder.common.R.string.intro_text_summary_title),
                    context.resources.getString(com.soundrecorder.common.R.string.intro_text_summary_desc)
                )
            )
            add(
                COUIUserStatementListItem(
                    ContextCompat.getDrawable(context, R.drawable.intro_content_share),
                    context.resources.getString(com.soundrecorder.common.R.string.intro_content_share_title),
                    context.resources.getString(com.soundrecorder.common.R.string.intro_content_share_desc)
                )
            )
        }
    }

    private fun doDismiss(type: Int, isPerformAnim: Boolean = true, remove: Boolean = false) {
        val dialog = dialogMap[type] ?: return
        if (dialog is COUIBottomSheetDialog) {
            dialog.dismiss(isPerformAnim)
        } else {
            dialog.dismiss()
        }
        /*重建、每次onresume隐藏再显示都是用的该map,所以弹窗消失不代表没有弹窗，不能随便remove*/
        if (remove) {
            dialogMap.remove(type)
        }
    }

    private fun COUIBottomSheetDialog.addAndShowDialog(type: Int) {
        show()
        DebugUtil.d(TAG, "addAndShowDialog: dialog type = $type, show() has been called!")
        doDismiss(type)
        dialogMap[type] = this
        syncPrivacyDialogShowingType = type
    }

    /**
     * 用户须知-总概览-欢迎使用
     */
    private fun doShowDialogUserNotice(pageFrom: Int? = null) {
        val title = context.resources.getString(com.soundrecorder.common.R.string.welcome_use)
        val message = context.resources.getString(com.soundrecorder.common.R.string.privacy_policy_user_notice_all_exp)
        val protocolText = createSpan(
            TYPE_USER_NOTICE,
            com.soundrecorder.common.R.string.privacy_policy_user_notice_view_info_v2,
            com.soundrecorder.common.R.string.privacy_policy_privacy_policy_new,
            com.soundrecorder.common.R.string.sound_record_user_agreement_span
        )
        val ok = context.resources.getString(com.soundrecorder.common.R.string.full_page_statement_button_text)
        val cancel = context.resources.getString(com.soundrecorder.common.R.string.runtime_permission_runtime_cancel_new)
        val dialog = createUserNoticeDialog(
            title = title,
            message = message,
            protocol = protocolText,
            ok = ok,
            cancel = cancel,
            clickOk = {
                doDismiss(TYPE_USER_NOTICE)
                handleUserStatementChange(true)
                onClickFromPrivacyPolicy(
                    TYPE_USER_NOTICE,
                    arrayListOf(TYPE_PERMISSION_SMART_SHORTHAND, TYPE_PERMISSION_SUMMARY_MIND, TYPE_PERMISSION_CLOUD, TYPE_PERMISSION_FEEDBACK)
                )
                PermissionUtils.setUserStatementGranted(true)
            },
            clickCancel = {
                doDismiss(TYPE_USER_NOTICE)
                handleUserStatementChange(false)
                delayInMain(context, DELAY_TIME) {
                    context.finish()
                }
            },
            clickBack = {
                context.finish()
            },
        )
        dialog.addAndShowDialog(TYPE_USER_NOTICE)
    }

    private fun doShowDialogUserNoticeUpdate(pageFrom: Int? = null) {
        val title = context.resources.getString(com.soundrecorder.common.R.string.welcome_use)
        val message = context.resources.getString(com.soundrecorder.common.R.string.user_notice_update_content_for_ck_exp)
        val protocol = createSpan(
            TYPE_PERMISSION_USER_NOTICE_UPDATE,
            com.soundrecorder.common.R.string.privacy_policy_user_notice_view_info_v2,
            com.soundrecorder.common.R.string.privacy_policy_privacy_policy,
            com.soundrecorder.common.R.string.sound_record_user_agreement_span
        )
        val ok = context.resources.getString(com.soundrecorder.common.R.string.full_page_statement_button_text)
        val cancel = context.resources.getString(com.soundrecorder.common.R.string.runtime_permission_runtime_cancel_new)
        val dialog = createUserNoticeDialog(
            title = title,
            message = message,
            protocol = protocol,
            ok = ok,
            cancel = cancel,
            clickOk = {
                doDismiss(TYPE_PERMISSION_USER_NOTICE_UPDATE)
                //开启全量功能+网络权限且弹窗消失
                onClickFromPrivacyPolicy(
                    TYPE_PERMISSION_USER_NOTICE_UPDATE,
                    arrayListOf(TYPE_PERMISSION_SMART_SHORTHAND, TYPE_PERMISSION_CLOUD, TYPE_PERMISSION_SUMMARY_MIND, TYPE_PERMISSION_FEEDBACK)
                )
                PermissionUtils.setUserStatementUpdateGranted(true)
                removeStatementView()
            },
            clickCancel = {
                doDismiss(TYPE_PERMISSION_USER_NOTICE_UPDATE)
                handleUserStatementChange(false)
                delayInMain(context, DELAY_TIME) {
                    context.finish()
                }
            },
            clickBack = {
                context.finish()
            }
        )
        dialog.addAndShowDialog(TYPE_PERMISSION_USER_NOTICE_UPDATE)
    }

    private fun doShowDialogAIEngine() {
        if (!PermissionUtils.isUserStatementGranted()) {
            DebugUtil.i(TAG, "doShowDialogAIEngine user statement not allowed")
            return
        }

        AIUnitApi.handleSmartNamePlugin(context) { result ->
            DebugUtil.i(TAG, "doShowDialogAIEngine handleSmartNamePlugin result: $result")
        }
    }

    /**
     * onAgreeClick
     */
    private fun onClickFromPrivacyPolicy(type: Int, grantedTypeList: List<Int>) {
        DebugUtil.i(TAG, "onClickFromPrivacyPolicy type: $type, grantedTypeList:${grantedTypeList.toList()}")
        if (PermissionUtils.shouldNetworkGrantedByFunc(grantedTypeList)) {
            PermissionUtils.setNetWorkGrantedStatus(context, true)
        }
        PermissionUtils.setStatementUpdateStatus(context)
        removeStatementView()

        val updateSpRequestPermission = if (BaseUtil.isAndroidROrLater) {
            PermissionUtils.hasAllFilePermission()
        } else {
            true
        }
        DebugUtil.d(TAG, "onClickFromPrivacyPolicy, updateSpRequestPermission:$updateSpRequestPermission")
        if (updateSpRequestPermission) {
            PermissionUtils.setNextActionForRequestPermission(context)
        } else {
            PermissionUtils.setNextActionForShowAllFileDialog(context)
        }
        resultCallback?.onPrivacyPolicySuccess(type)
    }

    private fun doShowDialog(type: Int, fromType: Int = TYPE_USER_NOTICE_DEFAULT, pageFrom: Int? = null) {
        currentShowingDialogType = type
        DebugUtil.d(TAG, "doShowDialog $type: $fromType")
        when (type) {
            TYPE_USER_NOTICE_DEFAULT, TYPE_USER_NOTICE, TYPE_USER_NOTICE_LIGHT_OS -> doShowDialogUserNotice(pageFrom)
            TYPE_PERMISSION_USER_NOTICE_UPDATE -> doShowDialogUserNoticeUpdate(pageFrom)
        }
    }

    @Suppress("SpreadOperator")
    private fun createSpan(
        type: Int,
        messageId: Int,
        vararg links: Int
    ): SpannableStringBuilder {
        val args = links.map { context.resources.getString(it) }
        val value = context.resources.getString(messageId, *args.toTypedArray())
        return innerCreateSpanBuilder(type, value, *links)
    }

    private fun innerCreateSpanBuilder(type: Int, value: String, vararg links: Int): SpannableStringBuilder {
        val args = links.map { context.resources.getString(it) }
        val builder = SpannableStringBuilder(value)
        args.forEach { link ->
            val start = value.indexOf(link)
            val end = start + link.length
            if (start >= 0) {
                builder.setSpan(object : COUIStatementClickableSpan(context) {
                    override fun onClick(widget: View) {
                        onClickSpan(type, link)
                        widget.clearFocus()
                        widget.isPressed = false
                        widget.isSelected = false
                    }
                }, start, end, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            }
        }
        return builder
    }

    protected open fun onClickSpan(type: Int, link: String) {
        DebugUtil.d(TAG, "onClickSpan, type:$type, link:$link")
        when (link) {
            context.resources.getString(com.soundrecorder.common.R.string.privacy_policy_privacy_policy_new) -> {
                if (ClickUtils.isQuickClick()) {
                    return
                }

                settingApi?.launchBootRegPrivacy(context) {
                    disableDialog = it
                }
            }

            context.resources.getString(com.soundrecorder.common.R.string.sound_record_user_agreement_span) -> {
                if (ClickUtils.isQuickClick()) {
                    return
                }

                settingApi?.launchRecordPrivacy(
                    context,
                    com.soundrecorder.common.R.string.privacy_policy_settings_user_agreement_key
                )
            }
        }
    }

    fun isShowing(type: Int): Boolean = dialogMap[type]?.isShowing == true

    fun getShowingPolicyDialogType(): Int? = if (dialogMap[currentShowingDialogType]?.isShowing == true) currentShowingDialogType else null

    /**
     * onSaveInstance时获取showing的弹窗key
     */
    fun onSaveShowingDialog(): ArrayList<Int> {
        val keys = arrayListOf<Int>()
        dialogMap.map {
            if (isShowing(it.key)) {
                keys.add(it.key)
            }
        }
        showDialogKeys.clear()

        return keys
    }

    /**
     * onRestoreInstance时还原showing的key
     */
    fun onRestoreShowingDialog(dialogKeys: ArrayList<Int>?) {
        isReCreate = true
        if (dialogKeys != null) {
            showDialogKeys.addAll(dialogKeys)
        }
    }

    /**
     * 用户须知等弹窗activity走configurationChange时重建逻辑；
     * 单独抽取方法，若有页面需要在用户须知弹窗重建前执行，可在该方法super之前调用；
     * 目前这里主要针对RecorderActivity onConfigurationChanged 需要在setContentView后执行弹窗重建
     */
    fun reCreateStateDialog() {
        dialogMap.forEach {
            if (isShowing(it.key)) {
                doDismiss(it.key, false)
                doShowDialog(it.key)
            }
        }
    }

    fun clearAll() {
        dialogMap.forEach {
            if (isShowing(it.key)) {
                doDismiss(it.key, false)
            }
        }
        dialogMap.clear()
        showDialogKeys.clear()
        disableDialog.dismissWhenShowing()
        disableDialog = null
    }

    fun resumeShowDialog(checkType: Int, forceShow: Boolean, pageFrom: Int?) {
        DebugUtil.d(TAG, "resumeShowDialog, checkType:$checkType, forceShow:$forceShow")
        when (checkType) {
            TYPE_USER_NOTICE_DEFAULT,
            TYPE_USER_NOTICE,
            TYPE_USER_NOTICE_LIGHT_OS -> resumeShowUserNotice(checkType, pageFrom)

            TYPE_PERMISSION_USER_NOTICE_UPDATE -> resumeShowUserNoticeUpdate(pageFrom)
            TYPE_PERMISSION_AI_PRIVACY_GUIDE -> doShowDialogAIEngine()
        }
    }

    fun checkAndDismissDialog() {
        if (PermissionUtils.getNextAction() != PermissionUtils.SHOULD_SHOW_USER_NOTICE) {
            doDismiss(TYPE_USER_NOTICE, false)
            doDismiss(TYPE_USER_NOTICE_BASIC, false)
            doDismiss(TYPE_USER_NOTICE_BASIC_STILL, false)
            doDismiss(TYPE_PERMISSION_USER_NOTICE_FUNCTION, false)
            doDismiss(TYPE_USER_NOTICE_LIGHT_OS, false)
            //已经授予过更新弹窗权限
            if (PermissionUtils.checkPermissionUpdateAlreadyApply(context)) {
                removeStatementView()
            }
        }
    }

    /**
     * show用户须知弹窗
     */
    private fun resumeShowUserNotice(checkType: Int, pageFrom: Int?) {
        addStatementView(pageFrom)
        if (isReCreate) {
            DebugUtil.d(TAG, "resumeShowUserNotice:$checkType, isReCreate,showDialogKeys=$showDialogKeys")
            if (showDialogKeys.contains(checkType)) {
                showDialogKeys.forEach {
                    doShowDialog(it)
                }
            } else {
                doShowDialog(checkType)
            }
            isReCreate = false
        } else {
            DebugUtil.d(TAG, "resumeShowUserNotice:$checkType, dialogMap=${dialogMap.keys}")
            if (checkType == TYPE_USER_NOTICE_DEFAULT) {
                // 默认情况，根据内销、轻量显示对应第一个基本弹窗
                val none = dialogMap.none { isShowing(it.key) }
                if (none) {
                    if (BaseUtil.isLightOS()) {
                        doShowDialog(TYPE_USER_NOTICE_LIGHT_OS)
                    } else {
                        doShowDialog(TYPE_USER_NOTICE)
                    }
                }
            } else if (dialogMap.containsKey(checkType) && isShowing(checkType)) {
                // 当前显示的dialog正在显示
                DebugUtil.d(TAG, "target dialog is showing")
            } else {
                // 正在显示的dialog同目标dialog不匹配
                dialogMap.forEach {
                    doDismiss(it.key, false)
                }
                doShowDialog(checkType)
            }
        }
    }

    private fun resumeShowUserNoticeUpdate(pageFrom: Int?) {
        addStatementView(pageFrom)
        if (isReCreate) {
            showDialogKeys.forEach {
                doShowDialog(it)
            }
            isReCreate = false
        } else {
            val none = dialogMap.none { isShowing(it.key) }
            if (none) {
                doShowDialog(TYPE_PERMISSION_USER_NOTICE_UPDATE)
            }
        }
    }

    private fun handleUserStatementChange(allow: Boolean) {
        setAllFuncTypePermission(allow)
        smartNameAction?.setSmartNameSwitchStatus(context, allow, true)
    }
}