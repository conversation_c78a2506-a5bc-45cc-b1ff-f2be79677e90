/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecorderService.java
 Description:
 Version: 1.0
 Date: 2009-12-15
 Author: zhanghr
 -----------Revision History-----------
 <author> <date> <version> <desc>
 zhanghr 2022/12/22 1.0 create
 */

// OPLUS Java File Skip Rule:MethodLength,MethodComplexity
package com.soundrecorder.recorderservice;

import static com.soundrecorder.modulerouter.recorder.RecordInterfaceKt.FROM_SWITCH_RECORD_STATUS_NORMAL;
import static com.soundrecorder.modulerouter.recorder.RecorderServiceInterfaceKt.FROM_SWITCH_RECORD_STATUS_APP_CARD;
import static com.soundrecorder.modulerouter.recorder.RecorderServiceInterfaceKt.FROM_SWITCH_RECORD_STATUS_MINI;
import static com.soundrecorder.modulerouter.recorder.RecorderServiceInterfaceKt.FROM_SWITCH_RECORD_STATUS_SMALL_CARD;
import static com.soundrecorder.modulerouter.recorder.RecorderViewModelConstantKt.INIT;

import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.net.Uri;
import android.os.IBinder;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LifecycleKt;
import androidx.lifecycle.LifecycleService;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Observer;

import com.soundrecorder.base.BaseApplication;
import com.soundrecorder.base.StorageManager;
import com.soundrecorder.base.ext.ExtKt;
import com.soundrecorder.base.utils.AddonAdapterCompatUtil;
import com.soundrecorder.base.utils.BaseUtil;
import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.FeatureOption;
import com.soundrecorder.base.utils.PrefUtil;
import com.soundrecorder.base.utils.ToastManager;
import com.soundrecorder.common.base.PlayerHelperCallback;
import com.soundrecorder.common.buryingpoint.BuryingPoint;
import com.soundrecorder.common.buryingpoint.BuryingPointRealTimeSubtitle;
import com.soundrecorder.common.buryingpoint.RecorderUserAction;
import com.soundrecorder.common.buryingpoint.RecorderUserActionKt;
import com.soundrecorder.common.constant.RecordModeConstant;
import com.soundrecorder.common.constant.RecorderConstant;
import com.soundrecorder.common.databean.ConvertContentItem;
import com.soundrecorder.common.databean.MarkMetaData;
import com.soundrecorder.common.databean.markdata.MarkDataBean;
import com.soundrecorder.common.db.RecorderDBUtil;
import com.soundrecorder.common.manager.WakeLockManager;
import com.soundrecorder.common.permission.PermissionUtils;
import com.soundrecorder.common.realtimeasr.IRealtimeSubtitleCache;
import com.soundrecorder.common.realtimeasr.OnRealtimeListener;
import com.soundrecorder.common.realtimeasr.RealTimeAsrStatus;
import com.soundrecorder.common.task.RecordRouterManager;
import com.soundrecorder.common.utils.AudioNameUtils;
import com.soundrecorder.common.utils.FunctionOption;
import com.soundrecorder.common.utils.OPSettingUtils;
import com.soundrecorder.common.utils.RecordModeUtil;
import com.soundrecorder.common.utils.TimeSetUtils;
import com.soundrecorder.common.utils.UniDirectionalRecordUtils;
import com.soundrecorder.modulerouter.recorder.MarkAction;
import com.soundrecorder.modulerouter.recorder.RecordInterface;
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant;
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface;
import com.soundrecorder.modulerouter.utils.KoinInterfaceHelper;
import com.soundrecorder.recorderservice.controller.RecorderController;
import com.soundrecorder.recorderservice.controller.observer.RecordInfoSaveObserver;
import com.soundrecorder.recorderservice.controller.observer.WaveObserver;
import com.soundrecorder.recorderservice.manager.AudioModeChangeManager;
import com.soundrecorder.recorderservice.manager.MuteModeOperator;
import com.soundrecorder.recorderservice.manager.RecordExpandController;
import com.soundrecorder.recorderservice.manager.RecordProcessController;
import com.soundrecorder.recorderservice.manager.RecordStatusManager;
import com.soundrecorder.recorderservice.manager.RecorderBroadCastReceivers;
import com.soundrecorder.recorderservice.manager.RecorderUIController;
import com.soundrecorder.recorderservice.manager.RecorderViewModel;
import com.soundrecorder.recorderservice.manager.listener.RecordResultCallback;
import com.soundrecorder.recorderservice.manager.statusbar.RecordStatusBarUpdater;
import com.soundrecorder.recorderservice.recorder.binder.RecorderBinder;
import com.soundrecorder.wavemark.mark.MarkHelper;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import kotlin.Unit;

final public class RecorderService extends LifecycleService implements PlayerHelperCallback, RecorderBroadCastReceivers.IBroadCastReceiveCallback {


    private static final String TAG = "RecorderService";
    public static final String COMPONENT_SAFE_PERMISSION = "oppo.permission.OPPO_COMPONENT_SAFE";
    /**
     * 1.保存中、添加图片标记：false
     * 2.超过标记最大数量、暂停状态下的1s内已存在标记：false
     */
    private final MediatorLiveData<Boolean> mMarkEnable = new MediatorLiveData<>();
    private final MutableLiveData<Long> mLastMarkTime = new MutableLiveData<>();
    private final MutableLiveData<Integer> mSaveState = new MutableLiveData<>();
    /*定向录音开关状态,默认null*/
    private final MutableLiveData<Boolean> mDirectionalRecordOn = new MutableLiveData<>();
    /*定向录音按钮enable状态*/
    private final MutableLiveData<Boolean> mDirectionalRecordEnable = new MutableLiveData<>();

    private final MutableLiveData<String> mDirectionalRecordTime = new MutableLiveData<>();
    /* 最近一次打开定向录音的时间*/
    private final MutableLiveData<Long> mLastDirectionalRecordOnTime = new MutableLiveData<>();

    private final RecorderServiceInterface mRecorderViewModelApi = KoinInterfaceHelper.INSTANCE.getRecorderViewModelApi();
    private final RecordInterface mRecordApi = KoinInterfaceHelper.INSTANCE.getRecorderApi();

    public String mFileBeingRecorded = null;
    public boolean mMuteEnable = true;
    /**
     * true:①通话中、②保存音频过程
     */
    public MutableLiveData<Boolean> isBtnDisabled = new MutableLiveData<>();

    private boolean mCMCCVersion = false;
    private boolean mResumingFlag = false;
    private int mStartTryCount = 0;
    private long mDuration = 0;
    private Context mContext;
    private Boolean mHasSystemRecorderConflictToast = true;
    private Boolean mNeedStartAfterBind = false;
    private RecorderBinder mRecorderBinder;
    private RecordProcessController mRecordProcessController = null;
    private RecordExpandController.RecordConfig mRecordConfig = null;
    private RecordExpandController.OtherConfig mOtherConfig = null;
    private MuteModeOperator.MuteConfig mMuteConfig = null;
    private RecorderBroadCastReceivers mRecorderBroadCastReceivers = null;
    private RecordResultCallback mRecorderResultCallback = null;
    /**
     * 智能图片标记loading状态标记，PopViewLoadingActivity
     */
    private boolean mDoMultiPictureMarkLoading = false;

    private MarkHelper mMarkHelper;
    private int mNeedShowNotificationPermissionDeniedSnackBar = 0;
    /*记录是否调用startForeground*/
    private boolean mHasStartForeground = false;
    /*实时字幕埋点*/
    private BuryingPointRealTimeSubtitle mRealTimeSubtitleBuryingPoint = null;
    /*实时转写开关*/
    private boolean mRealTimeSwitch = false;
    private String mCurSpeechLanguage = RecorderViewModel.getInstance().getCurSelectedLanguage();


    public RecordProcessController getRecordProcessController() {
        return mRecordProcessController;
    }

    public RecordExpandController.RecordConfig getRecordConfig() {
        return mRecordConfig;
    }

    public RecordExpandController.OtherConfig getOtherConfig() {
        return mOtherConfig;
    }

    public void setNeedStartAfterBind(Boolean mNeedStartAfterBind) {
        this.mNeedStartAfterBind = mNeedStartAfterBind;
    }

    public Boolean getNeedStartAfterBind() {
        return mNeedStartAfterBind;
    }

    public MutableLiveData<Boolean> getMarkEnable() {
        return mMarkEnable;
    }

    public MutableLiveData<Long> getLastMarkTime() {
        return mLastMarkTime;
    }

    public MutableLiveData<Integer> getSaveState() {
        return mSaveState;
    }

    public void setMarkEnable(boolean enable) {
        ExtKt.postValueSafe(mMarkEnable, enable);
    }

    public void setDirectionalRecordOn(boolean enable) {
        ExtKt.postValueSafe(mDirectionalRecordOn, enable);
    }

    public void setDirectionalRecordTime(String directionalRecordTime) {
        ExtKt.postValueSafe(mDirectionalRecordTime, directionalRecordTime);
    }

    public MutableLiveData<String> getDirectionalRecordTime() {
        return mDirectionalRecordTime;
    }

    public void setLastDirectionalRecordOnTime(long time) {
        ExtKt.postValueSafe(mLastDirectionalRecordOnTime, time);
    }

    public MutableLiveData<Long> getLastDirectionalRecordOnTime() {
        return mLastDirectionalRecordOnTime;
    }

    public MutableLiveData<Boolean> getDirectionalRecordOn() {
        return mDirectionalRecordOn;
    }

    public MutableLiveData<Boolean> getDirectionalRecordEnable() {
        return mDirectionalRecordEnable;
    }

    public void setDirectionalRecordEnable(boolean enable) {
        ExtKt.postValueSafe(mDirectionalRecordEnable, enable);
    }

    @Override
    public boolean hasPaused() {
        int state = RecordStatusManager.getCurrentStatus();
        if ((state == RecordStatusManager.RECORDING) || getServiceNotificationIsRecordingState()) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public long getDuration() {
        return mDuration;
    }

    @Override
    public long getCurrentPlayerTime() {
        return getTime();
    }

    @Override
    public MutableLiveData<String> getPlayerName() {
        return null;
    }

    @Override
    public void onCurTimeChanged(long curTime) {
        // do nothing
    }


    public void addSourceForNotificationBtnDisabled(MutableLiveData<Boolean> addPictureMarking) {
        DebugUtil.d(TAG, "addSourceForNotificationBtnDisabled");
        mMarkEnable.addSource(addPictureMarking, aBoolean -> {
            if (mMarkHelper != null) {
                mMarkHelper.setAddPictureMark(aBoolean);
            }
            refreshMarkEnabled();
        });
    }

    private void setBtnDisabled(boolean value) {
        if (AudioModeChangeManager.isAudioModeChangePause()) {
            ExtKt.postValueSafe(isBtnDisabled, true);
        } else {
            ExtKt.postValueSafe(isBtnDisabled, value);
        }
    }

    public int addMark(boolean isFromNotification, @NonNull MarkMetaData pictureMarkMetaData) {
        if (mMarkHelper == null) {
            return -1;
        }
        if (pictureMarkMetaData.getCurrentTimeMillis() < 0) {
            //暂停状态下，解析的波形时间短于录制器时长getTime()
            long newTimeInMills = getRecordCurrentTime();
            pictureMarkMetaData.setCurrentTimeMillis(newTimeInMills);
        }
        int result = mMarkHelper.addMark(isFromNotification, pictureMarkMetaData);
        if (result >= 0) {
            ExtKt.postValueSafe(mLastMarkTime, mMarkHelper.getMarkCorrectTime(result));
            ExtKt.postValueSafe(mMarkEnable, isMarkEnabled());
            RecorderViewModel.getInstance().onMarkDataChange(MarkAction.SINGLE_ADD, result);
            HashMap<String, String> eventInfo = new HashMap<>();
            if (RecordModeUtil.isSupportMultiRecordMode(mContext)) {
                eventInfo.put("mode", String.valueOf(getRecordType()));
            } else {
                eventInfo.put("mode", "-1");
            }
            RecorderUserAction.addCommonUserAction(
                    this,
                    RecorderUserAction.USER_ACTION_MARK_TAG,
                    RecorderUserAction.EVENT_MARK_TAG_FROM_RECORDING,
                    eventInfo,
                    false
            );
        }
        return result;
    }

    /**
     * 获取当前录制的时间点
     *
     * @return 当前录制的时间，单位毫秒
     */
    public long getRecordCurrentTime() {
        int state = RecordStatusManager.getCurrentStatus();
        long newTimeInMills = 0;
        if ((state == RecordStatusManager.PAUSED)
                && (mRecordProcessController != null
                && mRecordProcessController.getMRecorderExpandController() != null)) {
            newTimeInMills = mRecordProcessController.getMRecorderExpandController().getTime();
        } else {
            newTimeInMills = getCurrentPlayerTime();
        }
        return newTimeInMills;
    }

    public int addMultiPictureMark(@NonNull ArrayList<MarkMetaData> files) {
        if (mMarkHelper == null) {
            return -1;
        }
        ArrayList<Integer> successIndex = mMarkHelper.addMultiPictureMark(files);
        if (!successIndex.isEmpty()) {
            ExtKt.postValueSafe(mMarkEnable, isMarkEnabled());
            int lastIndex = successIndex.get(successIndex.size() - 1);
            RecorderViewModel.getInstance().onMarkDataChange(MarkAction.MULTI_ADD, lastIndex);
            HashMap<String, String> eventInfo = new HashMap<>();
            if (RecordModeUtil.isSupportMultiRecordMode(mContext)) {
                eventInfo.put("mode", String.valueOf(getRecordType()));
            } else {
                eventInfo.put("mode", "-1");
            }
            RecorderUserAction.addCommonUserAction(
                    this,
                    RecorderUserAction.USER_ACTION_MARK_TAG,
                    RecorderUserAction.EVENT_MARK_TAG_FROM_RECORDING,
                    eventInfo,
                    false
            );
        }
        return successIndex.size();
    }

    public boolean isMarkEnabled() {
        if (mMarkHelper == null) {
            return true;
        }

        if (RecorderViewModel.getInstance().isRecordSaving()) {
            return false;
        }
        return mMarkHelper.isMarkEnabled();
    }

    public List<MarkDataBean> getMarkDatas() {
        if (mMarkHelper == null) {
            return null;
        }
        return mMarkHelper.getMarkDatas().getValue();
    }

    public void removeMark(int index) {
        if (mMarkHelper == null) {
            return;
        }
        MarkDataBean removeItem = mMarkHelper.removeMark(index);
        if (removeItem != null) {
            HashMap<String, String> eventInfo = new HashMap<>();
            if (RecordModeUtil.isSupportMultiRecordMode(this)) {
                eventInfo.put("recordmode", String.valueOf(getRecordType()));
            } else {
                eventInfo.put("recordmode", "-1");
            }
            RecorderUserAction.addCommonUserAction(getApplicationContext(),
                    RecorderUserAction.USER_ACTION_MARK_TAG,
                    RecorderUserAction.EVENT_DELETE_MARK_TAG_FROM_RECORDING,
                    eventInfo, false);
            ExtKt.postValueSafe(mMarkEnable, isMarkEnabled());
            RecorderViewModel.getInstance().onMarkDataChange(MarkAction.DELETE, 0);
        }
    }

    public boolean renameMark(String text, String newImage, int index) {
        if (mMarkHelper == null) {
            return false;
        }
        boolean flag = mMarkHelper.renameMark(text, newImage, index);
        if (flag) {
            HashMap<String, String> eventInfo = new HashMap<>();
            if (RecordModeUtil.isSupportMultiRecordMode(this)) {
                eventInfo.put("recordmode", String.valueOf(getRecordType()));
            } else {
                eventInfo.put("recordmode", "-1");
            }
            RecorderUserAction.addCommonUserAction(getApplicationContext(),
                    RecorderUserAction.USER_ACTION_MARK_TAG,
                    RecorderUserAction.EVENT_RENAME_MARK_TAG_FROM_RECORDING,
                    eventInfo, false);
            RecorderViewModel.getInstance().onMarkDataChange(MarkAction.RENAME, 0);
        }
        return flag;
    }

    public void refreshMarkEnabled() {
        ExtKt.postValueSafe(getMarkEnable(), isMarkEnabled());
    }

    @Override
    public void playBtnClick() {
        handleForFastRecord("");
    }

    public void handleForFastRecord(String from) {
        try {
            int state = RecordStatusManager.getCurrentStatus();
            switch (state) {
                case RecordStatusManager.HALT_ON:
                    DebugUtil.d(TAG, "onClick HALT_ON");
                    if (!StorageManager.getInstance(getApplicationContext()).checkStorageSpaceForRecordFile(true)) {
                        return;
                    }
                    //startSample();
                    break;
                case RecordStatusManager.PAUSED:
                    DebugUtil.d(TAG, "onClick PAUSED");
                    if (AudioModeChangeManager.isAudioModeChangePause()) {
                        ToastManager.showShortToast(mContext, com.soundrecorder.common.R.string.in_call_not_record);
                    } else {
                        addBuryPointRecordStatus(state, from);
                        resume();
                    }
                    /*if (resume()) {
                        startSample();
                    }*/
                    break;
                case RecordStatusManager.RECORDING:
                    DebugUtil.d(TAG, "onClick middleControl RECORDING");
                    addBuryPointRecordStatus(state, from);
                    pause();
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, e.getMessage());
        }
    }

    private void addBuryPointRecordStatus(int currentStatus, String from) {
        switch (from) {
            case FROM_SWITCH_RECORD_STATUS_NORMAL:
                if (currentStatus == RecordStatusManager.PAUSED) {
                    BuryingPoint.addClickRecordState(RecorderUserAction.VALUE_PAUSE_RECORD);
                } else if (currentStatus == RecordStatusManager.RECORDING) {
                    BuryingPoint.addClickRecordState(RecorderUserAction.VALUE_CONTINUE_RECORD);
                }
                break;
            case FROM_SWITCH_RECORD_STATUS_SMALL_CARD:
                if (currentStatus == RecordStatusManager.PAUSED) {
                    BuryingPoint.addClickRecordStateInSmallCard(RecorderUserAction.VALUE_PAUSE_RECORD_SMALL_CARD);
                } else if (currentStatus == RecordStatusManager.RECORDING) {
                    BuryingPoint.addClickRecordStateInSmallCard(RecorderUserAction.VALUE_CONTINUE_RECORD_SMALL_CARD);
                }
                break;
            case FROM_SWITCH_RECORD_STATUS_MINI:
                if (currentStatus == RecordStatusManager.PAUSED) {
                    BuryingPoint.addClickRecordStateInMiniCard(RecorderUserAction.VALUE_PAUSE_RECORD_MINI_CARD);
                } else if (currentStatus == RecordStatusManager.RECORDING) {
                    BuryingPoint.addClickRecordStateInMiniCard(RecorderUserAction.VALUE_CONTINUE_RECORD_MINI_CARD);
                }
                break;
            case FROM_SWITCH_RECORD_STATUS_APP_CARD:
                if (currentStatus == RecordStatusManager.PAUSED) {
                    BuryingPoint.addClickRecordStateInDragonFlyCard(RecorderUserAction.VALUE_PAUSE_RECORD_DRAGONFLY_CARD);
                } else if (currentStatus == RecordStatusManager.RECORDING) {
                    BuryingPoint.addClickRecordStateInDragonFlyCard(RecorderUserAction.VALUE_CONTINUE_RECORD_DRAGONFLY_CARD);
                }
                break;
            default:
                break;
        }
    }

    @NonNull
    @Override
    public String getKeyId() {
        long keyId = -1;
        if (mRecordProcessController != null && mRecordProcessController.getMRecorderExpandController() != null
                && mRecordProcessController.getMRecorderExpandController().getMRecordFileWraper() != null) {
            keyId = mRecordProcessController.getMRecorderExpandController().getMRecordFileWraper().getMRecordDbId();
        }
        if (keyId < 0) {
            String filePath = getRecorderAbsolutePath();
            return RecorderDBUtil.getKeyIdByPath(filePath);
        }
        return String.valueOf(keyId);
    }

    public void setRecorderStateListener(RecorderUIController.RecorderStateListener listener) {
        if (mRecordProcessController != null) {
            mRecordProcessController.registerUICallback(listener);
        }
    }

    @Override
    public void onCreate() {
        super.onCreate();
        DebugUtil.i(TAG, "created " + this);
        mContext = this.getApplicationContext();
        mHasSystemRecorderConflictToast = FeatureOption.OPLUS_MULTIMEDIA_RECORD_CONFLICT;
        mCMCCVersion = FeatureOption.OPLUS_NEW_SOUND_RECORDER_SETTING;

        if (mCMCCVersion || FeatureOption.IS_PAD) {
            mMuteEnable = false;
        }
        DebugUtil.i(TAG, "onCreate mMuteEnable = " + mMuteEnable);
        boolean needListenAudioFocus = !mHasSystemRecorderConflictToast && !BaseUtil.isAndroidQOrLater();
        mRecordConfig = new RecordExpandController.RecordConfig();
        mOtherConfig = new RecordExpandController.OtherConfig();
        mOtherConfig.setFromOtherApp(RecorderViewModel.getInstance().isFromOtherAppOrigin());
        mMuteConfig = new MuteModeOperator.MuteConfig(mMuteEnable, needListenAudioFocus);
        DebugUtil.i(TAG, "onCreate recordConfig: " + mRecordConfig + ", mOtherConfig: " + mOtherConfig + ", mMuteConfig: " + mMuteConfig);
        mRecordProcessController = new RecordProcessController(this, mRecordConfig, mOtherConfig, mMuteConfig);
        mRecordProcessController.initArgs();
        mRecordProcessController.initHandlerThread();

        registerReceivers();

        //todo
        initRecorderControllerObserver();

        if (mMarkHelper == null) {
            mMarkHelper = new MarkHelper(this, LifecycleKt.getCoroutineScope(getLifecycle()), true);
        }
        RecorderViewModel.getInstance().onServiceCreate(this);
        new TimeSetUtils(this, (action) -> {
            // 修复切换系统时间波形异常
            if (RecordStatusManager.getCurrentStatus() != RecordStatusManager.RECORDING) {
                return Unit.INSTANCE;
            }
            RecordProcessController recordProcessController = getRecordProcessController();
            if (recordProcessController == null) {
                return Unit.INSTANCE;
            }
            RecorderUIController recorderUIController = recordProcessController.getMRecorderUIController();
            if (recorderUIController != null) {
                recorderUIController.startSample();
            }
            return Unit.INSTANCE;
        });
    }

    private void registerReceivers() {
        if (mRecorderBroadCastReceivers == null) {
            DebugUtil.i(TAG, "registerReceivers");
            mRecorderBroadCastReceivers = new RecorderBroadCastReceivers();
            mRecorderBroadCastReceivers.setNeedRegisterCameraReceiver(mHasSystemRecorderConflictToast);
            mRecorderBroadCastReceivers.registerBroadcastReceivers(this);
            mRecorderBroadCastReceivers.setOnBroadCastReceiveCallback(this);
            if (mRecordProcessController != null && mRecordProcessController.getMRecorderExpandController() != null) {
                mRecorderBroadCastReceivers.setMMuteModeChangeListener(mRecordProcessController.getMRecorderExpandController().getMuteModeOperator());
            }
        }
    }

    private void unregisterReceivers() {
        if (mRecorderBroadCastReceivers != null) {
            DebugUtil.i(TAG, "unregisterReceivers");
            mRecorderBroadCastReceivers.setNeedRegisterCameraReceiver(mHasSystemRecorderConflictToast);
            mRecorderBroadCastReceivers.unRegisterBroadcastReceivers(this);
            mRecorderBroadCastReceivers.setOnBroadCastReceiveCallback(null);
            mRecorderBroadCastReceivers.setMMuteModeChangeListener(null);
            mRecorderBroadCastReceivers = null;
        }
    }


    private RecordExpandController.RecordConfig initRecordConfig(Intent intent) {
        /*默认mp3格式*/
        int defaultFormat = RecorderConstant.RECORDER_AUDIO_FORMAT_MP3;
        /**
         * 一加外销手机默认使用SP中存的音频格式
         * 外销融合，内外销可录制wav、aac、mp3，从SP中读取的音频格式，默认为mp3
         */
        if (FunctionOption.IS_SUPPORT_WAV_AND_AAC) {
            defaultFormat = OPSettingUtils.INSTANCE.getOPAudioFormat(mContext);
        }
        /*三方短信进来为AMR_NB，默认MP3*/
        int formate = intent.getIntExtra(RecorderDataConstant.SERVICE_RECORD_FORMAT, defaultFormat);
        int recordType = intent.getIntExtra(RecorderDataConstant.RECORDER_TYPE, RecordModeUtil.getModeValue());
        long maxSizeLimit = intent.getLongExtra(RecorderDataConstant.SERVICE_MAX_FILE_SIZE, 0);
        int maxDurationLimit = (int) intent.getLongExtra(RecorderDataConstant.SERVICE_MAX_DURATION, 0);
        DebugUtil.i(TAG, "initRecordConfig formate: " + formate + ", recordType: "
                + recordType + ", maxSizeLimit: " + maxSizeLimit + ", maxDurationLimit: " + maxDurationLimit);
        return new RecordExpandController.RecordConfig(formate, recordType, maxSizeLimit, maxDurationLimit);
        /*mRecordConfig = new RecordExpandController.RecordConfig(formate, recordType, maxSizeLimit, maxDurationLimit);*/
    }


    private RecordExpandController.OtherConfig initOtherConfig(Intent intent) {
        String extraLaunchFrom = intent.getStringExtra(RecorderDataConstant.PAGE_FROM_NAME);
        RecordRouterManager.getInstance().setRecordFrom(extraLaunchFrom);
        boolean isFromOtherApp = intent.getBooleanExtra(RecorderDataConstant.SERVICE_IS_FROM_OTHER_APP, false);
        int recordFromType = RecordExpandController.OtherConfig.START_FROM_NORMAL;
        if (!TextUtils.isEmpty(extraLaunchFrom)) {
            if (RecorderDataConstant.PAGE_FROM_SLIDE_BAR.equalsIgnoreCase(extraLaunchFrom)) {
                recordFromType = RecordExpandController.OtherConfig.START_FROM_SLIDBAR;
            } else if (RecorderDataConstant.PAGE_FROM_BREENO.equalsIgnoreCase(extraLaunchFrom)) {
                recordFromType = RecordExpandController.OtherConfig.START_FROM_BREENO;
            } else if (RecorderDataConstant.PAGE_FROM_BREENO_FRONT.equalsIgnoreCase(extraLaunchFrom)) {
                recordFromType = RecordExpandController.OtherConfig.START_FROM_BREENO_FRONT;
            } else if (RecorderDataConstant.PAGE_FROM_DRAGON_FLY.equals(extraLaunchFrom)) {
                recordFromType = RecordExpandController.OtherConfig.START_FROM_APP_CARD;
            } else if (RecorderDataConstant.PAGE_FROM_SMALL_CARD.equals(extraLaunchFrom)) {
                recordFromType = RecordExpandController.OtherConfig.START_FROM_SMALL_CARD;
            } else if (RecorderDataConstant.PAGE_FROM_MINI_APP.equals(extraLaunchFrom)) {
                recordFromType = RecordExpandController.OtherConfig.START_FROM_MINI_APP;
            } else if (RecorderDataConstant.PAGE_FROM_CUBE_BUTTON.equals(extraLaunchFrom)) {
                recordFromType = RecordExpandController.OtherConfig.START_FROM_CUBE_BUTTON;
            } else if (RecorderDataConstant.PAGE_FROM_LOCK_SCREEN.equals(extraLaunchFrom)) {
                recordFromType = RecordExpandController.OtherConfig.START_FROM_LOCK_SCREEN;
            } else {
                recordFromType = RecordExpandController.OtherConfig.START_FROM_NORMAL;
            }
        }
        boolean isNeedResult = intent.getBooleanExtra(RecorderDataConstant.SERVICE_NEED_RESULT, false);
        DebugUtil.i(TAG, "initOtherConfig isFromOtherApp: " + isFromOtherApp
                + ", recordFromType: " + recordFromType + ", isNeedResult: " + isNeedResult);
        return new RecordExpandController.OtherConfig(isFromOtherApp, recordFromType, isNeedResult);
    }

    /**
     * 启动录音服务 启动类型 进入录音app -> 1 启动录制音频 -> 2
     * 判断 不为1 说明 未启动进入录音app且录音服务启动 是后台录制音频
     */
    public void buryEntryLaunch() {
        if (!Objects.equals(BaseUtil.sValueEntryType, BaseUtil.VALUE_KEY_ENTRY_TYPE_APP)) {
            BaseUtil.sValueEntryType = BaseUtil.VALUE_KEY_ENTRY_TYPE_RECORD;
            BuryingPoint.addRecordEntryLaunch(BaseUtil.sValueEntryType, BaseUtil.VALUE_KEY_LANDING_TYPE_BACK);

        }
    }

    private void buryingPointStartTime() {
        /* 初始化启动时间 启动录音服务 = true */
        BaseUtil.sStartRecordingService = true;
        if (BaseUtil.sStartTime == 0L) {
            BaseUtil.sStartTime = System.currentTimeMillis();
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        super.onStartCommand(intent, flags, startId);
        /* 初始化启动时间 */
        buryingPointStartTime();
        /* 入口启动 埋点 */
        buryEntryLaunch();
        /*实时字幕埋点实例化*/
        mRealTimeSubtitleBuryingPoint = new BuryingPointRealTimeSubtitle();
        DebugUtil.d(TAG, "onStartCommand");
        if (intent != null) {
            RecordExpandController.OtherConfig otherConfig = initOtherConfig(intent);
            RecordExpandController.RecordConfig recordConfig = initRecordConfig(intent);
            boolean needUpdateRecordConfig = intent.getBooleanExtra(RecorderDataConstant.SERVICE_NEED_UPDATE_RECORDCONFIG, true);
            boolean needUpdateOtherConfig = intent.getBooleanExtra(RecorderDataConstant.SERVICE_NEED_UPDATE_OTHERCONFIG, true);
            if (intent.getBooleanExtra(RecorderDataConstant.SERVICE_NEED_SHOW_SNACKBAR, false)) {
                if (mNeedShowNotificationPermissionDeniedSnackBar <= 0) {
                    mNeedShowNotificationPermissionDeniedSnackBar = 1;
                }
            }
            DebugUtil.i(TAG, "onStartCommand recordConfig: " + recordConfig + ", mOtherConfig: "
                    + otherConfig + ", mMuteConfig: " + mMuteConfig + ", currentStatus: " + RecordStatusManager.INSTANCE.getCurrentStatus()
                    + ", needUpdateRecordConfig: " + needUpdateRecordConfig + ", needUpdateOtherConfig: " + needUpdateOtherConfig);
            setBtnDisabled(false);
            if (mRecordProcessController != null) {
                if (needUpdateRecordConfig) {
                    mRecordProcessController.updateRecordConfig(recordConfig);
                    mRecordConfig = recordConfig;
                }
                if (needUpdateOtherConfig) {
                    mRecordProcessController.updateOtherConfig(otherConfig);
                    mOtherConfig = otherConfig;
                }
                if ((RecordStatusManager.getCurrentStatus() == RecordStatusManager.INIT)
                        || (RecordStatusManager.getCurrentStatus() == RecordStatusManager.HALT_ON)) {
                    DebugUtil.i(TAG, "onStartCommand updateConfig: " + mRecordConfig
                            + ", mOtherConfig: " + mOtherConfig + ", mMuteConfig: " + mMuteConfig);
                    if (mRecordProcessController != null) {
                        mRecordProcessController.initRecordUIControllerForSlidBar();
                    }
                    if (otherConfig.isFromSlidBar()) {
                        start();
                        DebugUtil.i(TAG, "start record from slid bar");
                    }
                    if (mRecordApi != null && ((otherConfig.isFromBreno()) || (otherConfig.isFromBrenoFront()))) {
                        //这个地方告诉RecordStatService 可以bindRecordService了
                        DebugUtil.i(TAG, "onStartCommand FromBreno || FromBrenoFront");
                        mRecordApi.sendStartForegroundServiceBroadCast();
                    }
                    if (intent.getBooleanExtra(RecorderDataConstant.IS_NEED_RESULT, false)) {
                        RecorderController recorderController = getRecorderController();
                        if (recorderController != null) {
                            recorderController.mIsNeedResult = true;
                        }
                    }
                }
            }

            boolean autoStartService = intent.getBooleanExtra(RecorderDataConstant.SERVICE_AUTO_START_RECORD, false);
            if (autoStartService) {
                if (PermissionUtils.hasReadAudioPermission() && PermissionUtils.hasRecordAudioPermission()) {
                    DebugUtil.i(TAG, "auto start record");
                    int status = RecordStatusManager.getCurrentStatus();
                    if (status == RecordStatusManager.INIT || status == RecordStatusManager.HALT_ON) {
                        start();
                    }
                } else {
                    DebugUtil.i(TAG, "not permission to auto start record");
                }
            }
        }

        DebugUtil.i(TAG, "updateNotification service for startCommand");
        updateNotification();
        return START_NOT_STICKY;
    }

    private void initRecorderControllerObserver() {
        DebugUtil.i(TAG, "initRecorderControllerObserver mRecordProcessController: " + mRecordProcessController);
        if (mRecordProcessController != null) {
            mRecordProcessController.initRecordUIControllerForSlidBar();
        }
        getDirectionalRecordOn().observe(this, isOn -> {
            if (RecordStatusManager.getCurrentStatus() != RecordStatusManager.PAUSED) {

                // 录制过程中，用户打开or关闭定向录音开关，改变定向模式；若为暂停，doStart中会设置，这里不需要处理
                UniDirectionalRecordUtils.setUniDirectionalRecordingSwitch(mContext, isOn);
                if (!isOn) {
                    RecordModeUtil.setRecordMode(mContext, getRecordType());
                }
            }
            // 改变setting值
            UniDirectionalRecordUtils.writeRecorderUnidirectionalModeSetting(mContext, isOn);
        });

        getDirectionalRecordTime().observe(this, new Observer<String>() {

            @Override
            public void onChanged(String time) {
                DebugUtil.d(TAG, "getDirectionalRecordTime:" + time);
            }
        });
    }

    public void registerRecorderControlObservers(@NonNull WaveObserver waveObserver,
                                                 @NonNull RecordInfoSaveObserver recordInfoSaveObserver) {

        DebugUtil.i(TAG, "registerRecorderControlObservers mRecordProcessController: " + mRecordProcessController);
        if (mRecordProcessController != null) {
            mRecordProcessController.registerRecorderControlObservers(waveObserver, recordInfoSaveObserver);
        }
    }

    public void unRegisterWaveUIObserver() {
        DebugUtil.i(TAG, "unRegisterWaveUIObserver mRecordProcessController: " + mRecordProcessController);
        if (mRecordProcessController != null) {
            mRecordProcessController.unRegisterWaveUIObserver();
        }
    }

    private void updateNotification() {
        DebugUtil.i(TAG, "updateNotification: " + mRecordProcessController);
        if (mRecordProcessController != null) {
            mRecordProcessController.updateNotification();
            mHasStartForeground = true;
        }
    }

    /**
     * 首次启动录音，通知栏状态显示状态不对，单从state是否是recording不能解决问题
     *
     * @return
     */
    private boolean getServiceNotificationIsRecordingState() {
        return RecordStatusManager.getCurrentStatus() == RecordStatusManager.INIT && PermissionUtils.hasReadAudioPermission();
    }

    @Override
    public void onTaskRemoved(Intent intent) {
        DebugUtil.i(TAG, "onTaskRemoved");
        if (!ignoreTaskRemoved()) {
            cancelRecordNotification();
            if (mRecorderViewModelApi != null) {
                mRecorderViewModelApi.forceHideRecordStatusBar(RecordStatusBarUpdater.FROM_SERVICE_END);
            }
        }
        super.onTaskRemoved(intent);
    }

    private boolean ignoreTaskRemoved() {
        if (RecorderViewModel.getInstance().isFromBreno()) {
            return true;
        } else if (RecorderViewModel.getInstance().isFromSlidBar()) {
            return true;
        } else if (RecorderViewModel.getInstance().isFromAppCard()) {
            return true;
        } else if (RecorderViewModel.getInstance().isFromSmallCard()) {
            return true;
        } else if (RecorderViewModel.getInstance().isFromOtherApp()) {
            return true;
        } else if (RecorderViewModel.getInstance().isFromMiniApp()) {
            return true;
        } else if (RecorderViewModel.getInstance().isFromCubeButton()) {
            return true;
        } else if (RecorderViewModel.getInstance().isFromLockScreen()) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public IBinder onBind(@NonNull Intent intent) {
        super.onBind(intent);
        DebugUtil.d(TAG, "onBind");
        mRecorderBinder = new RecorderBinder(this);
        //直接从RecordStatService binding场景，需要
        mNeedStartAfterBind = intent.getBooleanExtra(RecorderDataConstant.BIND_EXTRA_NEED_START_RECORD_AFTER_BIND, false);
        return mRecorderBinder;
    }

    public void registerRecordResultCallback(RecordResultCallback inputCallback) {
        DebugUtil.i(TAG, "registerRecordResultCallback " + inputCallback + ", mRecordProcessController: " + mRecordProcessController);
        if (mRecordProcessController != null) {
            mRecordProcessController.setMRecordResultCallback(inputCallback);
        }
    }

    public void unRegisterResultCallback() {
        DebugUtil.i(TAG, "unRegisterRecordResultCallback mRecordProcessController: " + mRecordProcessController);
        if (mRecordProcessController != null) {
            mRecordProcessController.setMRecordResultCallback(null);
        }
    }


    @Override
    public void onDestroy() {
        DebugUtil.i(TAG, "onDestroy Service " + this);
        if (RecordStatusManager.isAlreadyRecording()) {
            if (mRecordProcessController != null) {
                DebugUtil.i(TAG, "onDestroy stopRecord");
                mRecordProcessController.stopRecord(false);
            }
        }
        /**
         * stop(false);
         * mAudioFocusListener.clearOnDetach();
         */
        cancelRecordNotification();
        DebugUtil.i(TAG, "onDestroy service so cancel all Notification");
        WakeLockManager.getInstance(this, null).releaseWakeLock();
        if (mRecorderBinder != null) {
            mRecorderBinder.cleanStatus();
            mRecorderBinder = null;
        }

        unregisterReceivers();

        stopForeground(true);

        unRegisterWaveUIObserver();
        release();
        resetRecordRouter();
        mFileBeingRecorded = null;
        if (mMarkHelper != null) {
            mMarkHelper.clear();
            mMarkHelper = null;
        }
        super.onDestroy();
        ExtKt.postValueSafe(mSaveState, INIT);
        RecorderViewModel.getInstance().onServiceDestroy();
    }

    /**
     * 重置RecordRouter记录的值
     */
    private void resetRecordRouter() {
        RecordRouterManager routerManager = RecordRouterManager.getInstance();
        routerManager.resetRecordFrom();
    }

    public int getMaxAmplitude() {
        if (mRecordProcessController != null && mRecordProcessController.getMRecorderUIController() != null) {
            return mRecordProcessController.getMRecorderExpandController().getMaxAmplitude();
        }
        return 0;
    }

    public long getTime() {
        if (mRecordProcessController != null && mRecordProcessController.getMRecorderExpandController() != null) {
            long time = mRecordProcessController.getMRecorderExpandController().getTime();
            //DebugUtil.i(TAG, "getTime " + time);
            return time;
        }
        return 0;
    }

    public void cancelRecordNotification() {
        DebugUtil.i(TAG, "cancelRecordNotification 000");
        if (mRecordProcessController != null) {
            mRecordProcessController.cancelRecordNotification();
        }
    }

    public void pause() {
        if (mRecordProcessController != null) {
            mRecordProcessController.pauseRecord();
        }
    }

    public void resume() {
        if (mRecordProcessController != null) {
            mRecordProcessController.resumeRecord();
        }
    }

    public void start() {
        if (mRecordProcessController != null) {
            mRecordProcessController.startRecord();
        }
    }

    public void cancel() {
        if (mRecordProcessController != null) {
            mRecordProcessController.cancelRecord();
            /* 取消保存音频 */
            RecorderUserActionKt.sSaveRecord = RecorderUserAction.VALUE_RETURN_TYPE_CANCEL;
        }
    }

    public void release() {
        DebugUtil.d(TAG, "release");
        if (mRecordProcessController != null) {
            mRecordProcessController.release();
            mRecordProcessController = null;
        }
        buryingPointEndTime();
    }

    private void buryingPointEndTime() {
        /* 初始化结束时间 结束录音服务 = false */
        /* 结束录制音频 */
        BaseUtil.sStartRecordingService = false;
        if (BaseUtil.sEndTime == 0L && !BaseUtil.sStartRecordingActivity) {
            BaseUtil.sEndTime = System.currentTimeMillis();
            long durationTime = BaseUtil.sEndTime - BaseUtil.sStartTime;
            /* 录音使用时长 埋点 */
            BuryingPoint.addRecordDurationMessage(durationTime);
            BaseUtil.sStartTime = 0L;
            BaseUtil.sEndTime = 0L;
        }
    }

    private String getIdBySampleUri() {
        String sampleUriStr = mRecorderViewModelApi.getSampleUriStr();
        if (TextUtils.isEmpty(sampleUriStr)) {
            return "";
        }
        int lastSlashIndex = sampleUriStr.lastIndexOf('/');
        if (lastSlashIndex == -1 || lastSlashIndex == sampleUriStr.length() - 1) {
            return "";
        }
        return sampleUriStr.substring(lastSlashIndex + 1);
    }

    /**
     * 实时字幕埋点上报
     */
    public void buryingPointRealTimeSubtitle() {
        DebugUtil.d(TAG, "buryingPointRealTimeSubtitle");
        if (mRealTimeSubtitleBuryingPoint != null) {
            mRealTimeSubtitleBuryingPoint.setUniFileId(getIdBySampleUri());
            mRealTimeSubtitleBuryingPoint.reportBuryingPoint();
            mRealTimeSubtitleBuryingPoint = null;
        }
    }

    /**
     * 实时字幕讲话人编辑埋点上报
     */
    public void buryingPointRealTimeSubtitleSpeakerEdit() {
        DebugUtil.d(TAG, "buryingPointRealTimeSubtitleSpeakerEdit");
        /*目前实时字幕编辑讲话人是默认应用到全部讲话人*/
        if (RecorderUserActionKt.sEditCount > 0) {
            BuryingPoint.addRecordSpeakerEdit(
                    getIdBySampleUri(),
                    RecorderUserAction.VALUE_REALTIME_FROM, RecorderUserAction.VALUE_APPLY_ALL_YES, RecorderUserActionKt.sEditCount);
            /*结束埋点 讲话人编辑次数 恢复默认值*/
            RecorderUserActionKt.sEditCount = 0;
        }
    }

    public void stopService() {
        DebugUtil.i(TAG, "stopService");
        /* 实时字幕 埋点 */
        buryingPointRealTimeSubtitle();
        /* 实时字幕讲话人编辑 埋点 */
        buryingPointRealTimeSubtitleSpeakerEdit();
        /* 完整录制一次音频 埋点 并恢复 entry_from 默认值*/
        BuryingPoint.addRecordDurationNew(mRecorderViewModelApi.getAmplitudeCurrentTime(),
                mRecorderViewModelApi.getRecordType(),
                mRecorderViewModelApi.getSampleUriStr());
        RecorderUserActionKt.sValueEntryFrom = RecorderUserAction.VALUE_ENTRY_FROM_LAUNCHER;
        if (!mHasStartForeground) {
            DebugUtil.w(TAG, "stopService, hasStartForeground is false");
            updateNotification();
        }
        stopSelf();
    }

    @Override
    public void onShutDown() {
        DebugUtil.i(TAG, "onShutDown");
        /*stop();
        stopSelf();*/
    }

    @Override
    public void onStopRecord() {
        DebugUtil.i(TAG, "onStopRecord");
        /*stop();
        stopSelf();*/
    }

    public void saveRecordInfo(@NonNull String displayName, @NonNull String originalDisplayName, Boolean needStopService, int saveRecordFromWhere) {
        DebugUtil.d(TAG, "saveRecordInfo controller:" + mRecordProcessController);
        if (mRecordProcessController != null) {
            mRecordProcessController.saveRecordInfo(displayName, originalDisplayName, getMarkDatas(), needStopService, saveRecordFromWhere);
        }
    }

    public String stop() {
        return stop(true);
    }

    /**
     * play sound only when stop() from recourder ui
     *
     * @param playSound
     * @return
     */
    private String stop(boolean playSound) {
        DebugUtil.v(TAG, "stop playsound=" + playSound + " mResumingFlag:" + mResumingFlag
                + " mStartTryCount:" + mStartTryCount);
        Uri outUri = null;
        String uriString = null;
        if (mRecordProcessController != null) {
            mRecordProcessController.stopRecord(false);
            if (mRecordProcessController.getMRecorderExpandController() != null
                    && mRecordProcessController.getMRecorderExpandController().getMRecordFileWraper() != null) {
                outUri = mRecordProcessController.getMRecorderExpandController().getMRecordFileWraper().getSampleFileUri();
                if (outUri != null) {
                    uriString = outUri.toString();
                }
            }
        }
        return uriString;
    }

    public int getState() {
        return RecordStatusManager.getCurrentStatus();
    }

    public String getRecordFilePath() {
        if (mRecordProcessController != null && mRecordProcessController.getMRecorderExpandController() != null) {
            RecordExpandController.RecordFileWraper recordFileWraper = mRecordProcessController.getMRecorderExpandController().getMRecordFileWraper();
            if (recordFileWraper != null && recordFileWraper.getSampleFile() != null) {
                DebugUtil.i(TAG, "getRecordFilePath " + recordFileWraper.getSampleFile());
                return recordFileWraper.getSampleFile().getPath();
            }
        }
        DebugUtil.e(TAG, "getFilePath null");
        return null;
    }

    public Uri getSampleUri() {
        if (mRecordProcessController != null && mRecordProcessController.getMRecorderExpandController() != null) {
            RecordExpandController.RecordFileWraper recordFileWraper = mRecordProcessController.getMRecorderExpandController().getMRecordFileWraper();
            if (recordFileWraper != null && recordFileWraper.getSampleFileUri() != null) {
                DebugUtil.i(TAG, "getSampleUri " + recordFileWraper.getSampleFileUri());
                return recordFileWraper.getSampleFileUri();
            }
        }
        DebugUtil.e(TAG, "getSampleUri null");
        return null;
    }

    public String getDefaultDisplayName() {
        return AudioNameUtils.genSaveFileName(getRecordType(), getSuffix(), true);
    }

    public String getRelativePath() {
        if (mRecordProcessController != null && mRecordProcessController.getMRecorderExpandController() != null) {
            RecordExpandController.RecordFileWraper recordFileWraper = mRecordProcessController.getMRecorderExpandController().getMRecordFileWraper();
            if (recordFileWraper != null) {
                DebugUtil.i(TAG, "getSampleFileName " + recordFileWraper.getRelativePath());
                return recordFileWraper.getRelativePath();
            }
        }
        DebugUtil.e(TAG, "getRelativePath");
        return null;
    }

    public String getRecorderAbsolutePath() {
        return AddonAdapterCompatUtil.getInternalSdDirectory(BaseApplication.getAppContext())
                + File.separator + getRelativePath() + getSampleFileName();
    }

    public String getSampleFileName() {
        if (mRecordProcessController != null && mRecordProcessController.getMRecorderExpandController() != null) {
            RecordExpandController.RecordFileWraper recordFileWraper = mRecordProcessController.getMRecorderExpandController().getMRecordFileWraper();
            if (recordFileWraper != null) {
                DebugUtil.i(TAG, "getSampleFileName " + recordFileWraper.getSampleFileName());
                return recordFileWraper.getSampleFileName();
            }
        }
        DebugUtil.e(TAG, "getSampleUri null");
        return null;
    }

    public String getSuffix() {
        if (mRecordConfig != null) {
            return mRecordConfig.getSuffix();
        }
        return RecorderConstant.MP3_FILE_SUFFIX;
    }

    public String getMimeType() {
        if (mRecordConfig != null) {
            return mRecordConfig.getMimeType();
        }
        return null;
    }

    public Integer getRecordType() {
        if (mRecordConfig != null) {
            return mRecordConfig.getRecordType();
        }
        return RecordModeConstant.RECORD_TYPE_STANDARD;
    }

    public boolean isNeedShowNotificationPermissionDeniedSnackBar() {
        return mNeedShowNotificationPermissionDeniedSnackBar > 0;
    }

    public void resetNeedShowNotificationPermissionDeniedSnackBar() {
        mNeedShowNotificationPermissionDeniedSnackBar = 0;
    }

    public RecorderController getRecorderController() {
        if (mRecordProcessController != null && mRecordProcessController.getMRecorderUIController() != null) {
            return mRecordProcessController.getMRecorderUIController().getMRecorderController();
        }
        return null;
    }

    /**
     * @param recordId
     * @param keySet
     * @see RecorderService
     */
    public static void saveRecordIdWhenRecording(long recordId, String keySet) {
        Context context = BaseApplication.getAppContext();
        Set<String> set = PrefUtil.getStringSet(context, keySet, null);
        if (set == null) {
            set = new HashSet<>();
        }
        String id = String.valueOf(recordId);
        if (!set.contains(id)) {
            set.add(id);
            PrefUtil.putStringSet(context, keySet, set);
        }
    }

    /**
     * 移除SP中的recordId并返回是否包含结果
     *
     * @param id
     * @param keySet
     * @return
     */
    public static boolean removeRecordIdWhenRecordComplete(String id, String keySet) {
        if (TextUtils.isEmpty(id)) {
            return false;
        }
        Context context = BaseApplication.getAppContext();
        Set<String> set = PrefUtil.getStringSet(context, keySet, null);
        if (set == null) {
            return false;
        }
        boolean has = set.contains(id);
        if (has) {
            set.remove(id);
            PrefUtil.putStringSet(context, keySet, set);
        }
        return has;
    }

    public boolean isDoMultiPictureMarkLoading() {
        return mDoMultiPictureMarkLoading;
    }

    public void setDoMultiPictureMarkLoading(boolean doMultiPictureMarkLoading) {
        this.mDoMultiPictureMarkLoading = doMultiPictureMarkLoading;
    }

    @NonNull
    @Override
    public String getPlayerMimeType() {
        return "";
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        RecorderViewModel.getInstance().onConfigurationChanged();
    }

    public void changeAsrLanguage(String language) {
        mRecordProcessController.changeAsrLanguage(language);
    }

    public void registerRtAsrListener(OnRealtimeListener listener) {
        mRecordProcessController.registerRtAsrListener(listener);
    }

    public void unregisterRtAsrListener(OnRealtimeListener listener) {
        mRecordProcessController.unregisterRtAsrListener(listener);
    }

    public void startTranslationConfig() {
        mRecordProcessController.startTranslationConfig();
    }

    public List<ConvertContentItem> getAllAsrContent() {
        return mRecordProcessController.getAllAsrContent();
    }

    public RealTimeAsrStatus getRealtimeAsrStatus() {
        return mRecordProcessController.getRealtimeAsrStatus();
    }

    public void externalInitAsr() {
        mRecordProcessController.externalInitAsr();
    }

    public void externalStopAsr() {
        mRecordProcessController.externalStopAsr();
    }

    public boolean updateSpeakerName(int roleId, String newName) {
        return mRecordProcessController.updateSpeakerName(roleId, newName);
    }

    public IRealtimeSubtitleCache getRealtimeSubtitleCache() {
        return mRecordProcessController.getRealtimeSubtitleCache();
    }

    public boolean isRealTimeSwitch() {
        return mRealTimeSwitch;
    }

    public void setRealTimeSwitch(boolean mRealTimeSwitch) {
        this.mRealTimeSwitch = mRealTimeSwitch;
        if (mRealTimeSubtitleBuryingPoint != null) {
            mRealTimeSubtitleBuryingPoint.addState(mRealTimeSwitch);
        }
    }

    public BuryingPointRealTimeSubtitle getRealTimeSubtitleBuryingPoint() {
        return mRealTimeSubtitleBuryingPoint;
    }

    /**
     * 获取实际录制（用于保存到文件）的Recorder状态，
     *
     * @return 是否正在录制
     */
    public boolean isMainRecording() {
        return RecordStatusManager.getCurrentStatus() == RecordStatusManager.RECORDING;
    }

    public String getCurSpeechLanguage() {
        return mCurSpeechLanguage;
    }

    public void setCurSpeechLanguage(String mCurSelectedLanguage) {
        this.mCurSpeechLanguage = mCurSelectedLanguage;
    }
}
