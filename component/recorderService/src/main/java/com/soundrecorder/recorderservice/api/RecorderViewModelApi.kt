/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecorderViewModelApi
 Description:
 Version: 1.0
 Date: 2022/12/22
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2022/12/22 1.0 create
 */

package com.soundrecorder.recorderservice.api

import android.content.Intent
import android.net.Uri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.soundrecorder.common.databean.ConvertContentItem
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.common.realtimeasr.IRealtimeSubtitleCache
import com.soundrecorder.common.realtimeasr.OnRealtimeListener
import com.soundrecorder.common.realtimeasr.RealTimeAsrStatus
import com.soundrecorder.modulerouter.convertService.listener.RecorderControllerListener
import com.soundrecorder.modulerouter.recorder.IGetBuryingPointRealTimeSubtitleInstance
import com.soundrecorder.modulerouter.recorder.IGetSupportLanguage
import com.soundrecorder.modulerouter.recorder.INIT
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.recorderservice.manager.AudioModeChangeManager
import com.soundrecorder.recorderservice.manager.DiskStorageChecker
import com.soundrecorder.recorderservice.manager.RecordStatusManager
import com.soundrecorder.recorderservice.manager.RecorderViewModel
import com.soundrecorder.recorderservice.manager.statusbar.RecordStatusBarUpdater
import org.json.JSONObject

object RecorderViewModelApi : RecorderServiceInterface {
    private var fileState: Int = INIT

    override var saveFileState: Int
        get() = fileState
        set(value) {
            fileState = value
        }

    override fun startRecorderService(function: Intent.() -> Unit) {
        RecorderViewModel.startRecorderService(function)
    }

    override fun <T> getMarkData(): List<T> {
        return RecorderViewModel.getInstance().getMarkData() as List<T>
    }

    override fun getLastMarkTime(): Long {
        return RecorderViewModel.getInstance().getLastMarkTime()
    }

    override fun getRecordType(): Int {
        return RecorderViewModel.getInstance().getRecordType()
    }

    override fun getAmplitudeList(): List<Int> {
        return RecorderViewModel.getInstance().getAmplitudeList()
    }

    override fun getLatestAmplitude(): Int {
        return RecorderViewModel.getInstance().getLatestAmplitude()
    }

    override fun getMaxAmplitude(): Int {
        return RecorderViewModel.getInstance().getMaxAmplitude()
    }

    override fun getMarkEnabledLiveData(): LiveData<Boolean>? {
        return RecorderViewModel.getInstance().getMarkEnabledLiveData()
    }

    override fun isMarkEnabledFull(): Boolean {
        return RecorderViewModel.getInstance().isMarkEnabledFull()
    }

    override fun checkMarkDataMoreThanMax(): Boolean {
        return RecorderViewModel.getInstance().checkMarkDataMoreThanMax()
    }

    override fun isNeedShowNotificationPermissionDeniedSnackBar(): Boolean {
        return RecorderViewModel.getInstance().isNeedShowNotificationPermissionDeniedSnackBar()
    }

    override fun resetNeedShowNotificationPermissionDeniedSnackBar() {
        return RecorderViewModel.getInstance().resetNeedShowNotificationPermissionDeniedSnackBar()
    }

    override fun start() {
        RecorderViewModel.getInstance().start()
    }

    override fun resume() {
        RecorderViewModel.getInstance().resume()
    }

    override fun pause() {
        RecorderViewModel.getInstance().pause()
    }

    override fun cancel() {
        RecorderViewModel.getInstance().cancel()
    }

    override fun stop(): String? {
        return RecorderViewModel.getInstance().stop()
    }

    override fun cancelRecordNotification() {
        RecorderViewModel.getInstance().cancelRecordNotification()
    }

    override fun getSampleUri(): Uri? {
        return RecorderViewModel.getInstance().getSampleUri()
    }

    override fun getSampleUriStr(): String {
        return getSampleUri()?.toString() ?: ""
    }

    override fun getSuffix(): String? {
        return RecorderViewModel.getInstance().getSuffix()
    }

    override fun getRecordFilePath(): String? {
        return RecorderViewModel.getInstance().getRecordFilePath()
    }

    override fun getRelativePath(): String? {
        return RecorderViewModel.getInstance().getRelativePath()
    }

    override fun getSampleDisplayName(genNewNameWhenSampleNull: Boolean): String {
        return RecorderViewModel.getInstance().getSampleDisplayName(genNewNameWhenSampleNull)
    }

    /**
     * 获取录制模式名称
     */
    override fun getRecordModeName(): String {
        return RecorderViewModel.getInstance().getRecordModeName()
    }

    /**
     * 定向录音开关
     */
    override fun setDirectRecordSwitch(isOn: Boolean) {
        RecorderViewModel.getInstance().setDirectRecordingOn(isOn)
    }

    /**
     * 定向录音时间
     */
    override fun setDirectRecordTime(directTime: String) {
        RecorderViewModel.getInstance().setDirectRecordTime(directTime = directTime)
    }

    /**
     * 设置最近一次开启定向录音的时间
     */
    override fun setLastDirectRecordOnTime(time: Long) {
        RecorderViewModel.getInstance().setLastDirectRecordOnTime(time)
    }

    /**
     * 获取最近一次开启定向录音的时间
     */
    override fun getLastDirectRecordOnTime(): Long {
        return RecorderViewModel.getInstance().getLastDirectRecordOnTime()
    }

    @JvmStatic
    fun getDirectRecordTime(): String? {
        return RecorderViewModel.getInstance().getDirectRecordTime()
    }

    /**
     * 定向录音开关是否可用
     */
    override fun getDirectRecordEnable(): Boolean {
        return RecorderViewModel.getInstance().isDirectRecodingEnable()
    }

    /**
     * 定向录音开关状态
     */
    override fun getDirectRecordOn(): Boolean {
        return RecorderViewModel.getInstance().isDirectRecodingOn()
    }

    override fun getFileBeingRecorded(): String? {
        return RecorderViewModel.getInstance().getFileBeingRecorded()
    }

    override fun isQuickRecord(): Boolean {
        return RecorderViewModel.getInstance().isQuickRecord()
    }

    override fun saveRecordInfo(
        displayName: String?,
        originalDisplayName: String?,
        saveRecordFromWhere: Int,
        needStopService: Boolean
    ) {
        RecorderViewModel.getInstance()
            .saveRecordInfo(displayName, originalDisplayName, saveRecordFromWhere, needStopService)
    }

    override fun hasInitRecorderService(): Boolean {
        return RecorderViewModel.getInstance().hasInitRecorderService()
    }

    override fun stopService() {
        RecorderViewModel.getInstance().stopService()
    }

    override fun switchRecorderStatus(from: String) {
        RecorderViewModel.getInstance().switchRecorderStatus(from)
    }

    override fun <T> addMark(mark: T) {
        RecorderViewModel.getInstance().addMark(mark as MarkMetaData)
    }

    override fun <T> addMultiPictureMark(marks: ArrayList<T>): Int {
        return RecorderViewModel.getInstance().addMultiPictureMark(marks as ArrayList<MarkMetaData>)
    }

    override fun removeMark(index: Int) {
        RecorderViewModel.getInstance().removeMark(index)
    }

    override fun renameMark(newText: String, newImage: String, index: Int): Boolean {
        return RecorderViewModel.getInstance().renameMark(newText, newImage, index)
    }

    override fun isFromSlidBar(): Boolean {
        return RecorderViewModel.getInstance().isFromSlidBar()
    }

    override fun isFromAppCard(): Boolean {
        return RecorderViewModel.getInstance().isFromAppCard()
    }

    override fun isFromMiniApp(): Boolean {
        return RecorderViewModel.getInstance().isFromMiniApp()
    }

    override fun isFromSmallCard(): Boolean {
        return RecorderViewModel.getInstance().isFromSmallCard()
    }

    override fun isFromOtherApp(): Boolean {
        return RecorderViewModel.getInstance().isFromOtherApp()
    }

    override fun isFromBreno(): Boolean {
        return RecorderViewModel.getInstance().isFromBreno()
    }

    override fun isFromCubeButtonOrLockScreen(): Boolean {
        return RecorderViewModel.getInstance().isFromCubeButton()
    }

    override fun addSourceForNotificationBtnDisabled(addPictureMarking: MutableLiveData<Boolean>) {
        RecorderViewModel.getInstance().addSourceForNotificationBtnDisabled(addPictureMarking)
    }

    override fun addListener(listener: RecorderControllerListener) {
        RecorderViewModel.getInstance().addListener(listener)
    }

    override fun removeListener(listener: RecorderControllerListener) {
        RecorderViewModel.getInstance().removeListener(listener)
    }

    override fun setDoMultiPictureMarkLoading(doMultiPictureMarkLoading: Boolean) {
        RecorderViewModel.getInstance().setDoMultiPictureMarkLoading(doMultiPictureMarkLoading)
    }

    override fun isStartServiceFromOtherApp(): Boolean {
        return RecorderViewModel.getInstance().isStartServiceFromOtherApp()
    }

    override fun getCurrentStatus(): Int {
        return RecordStatusManager.getCurrentStatus()
    }

    override fun getRecordStatusBeforeSaving(): Int? {
        return RecorderViewModel.getInstance().getRecordStatusBeforeSave()
    }

    override fun getLastStatus(): Int {
        return RecordStatusManager.getLastStatus()
    }

    override fun isAlreadyRecording(): Boolean {
        return RecordStatusManager.isAlreadyRecording()
    }

    override fun isRecordSaving(): Boolean {
        return RecorderViewModel.getInstance().isRecordSaving()
    }

    override fun checkModeCanRecord(needToast: Boolean): Boolean {
        return AudioModeChangeManager.checkModeCanRecord(needToast)
    }

    override fun isAudioModeChangePause(): Boolean {
        return AudioModeChangeManager.isAudioModeChangePause()
    }

    override fun isNeedResume(): Boolean {
        return AudioModeChangeManager.isNeedResume()
    }

    override fun checkDistBeforeStartRecord(): Boolean {
        return DiskStorageChecker.checkDistBeforeStartRecord()
    }

    override fun hasInitAmplitude(): Boolean {
        return RecorderViewModel.getInstance().hasInitAmplitude()
    }

    override fun getAmplitudeCurrentTime(): Long {
        return RecorderViewModel.getInstance().getAmplitudeCurrentTime()
    }

    override fun forceHideRecordStatusBar(from: String) {
        RecordStatusBarUpdater.forceDismiss(from)
    }

    override fun getSeedlingData(): JSONObject? {
        return RecordStatusBarUpdater.getSeedlingData()
    }

    override fun showOrHideStatusBar(from: String) {
        RecordStatusBarUpdater.showOrHide(from)
    }

    override fun onSeedlingCardStateChanged(isShow: Boolean) {
        RecordStatusBarUpdater.onSeedlingCardStateChanged(isShow)
    }

    override fun fluidCardDismiss(from: String) {
        RecordStatusBarUpdater.fluidCardDismiss(from)
    }

    override fun getSaveProgressValue(): Int {
        return RecorderViewModel.getInstance().getSaveProgressValue()
    }

    override fun changeAsrLanguage(language: String) {
        RecorderViewModel.getInstance().changeAsrLanguage(language)
    }

    override fun registerRtAsrListener(listener: Any) {
        if (listener is OnRealtimeListener) {
            RecorderViewModel.getInstance().registerRtAsrListener(listener)
        }
    }

    override fun unregisterRtAsrListener(listener: Any) {
        if (listener is OnRealtimeListener) {
            RecorderViewModel.getInstance().unregisterRtAsrListener(listener)
        }
    }

    override fun startTranslationConfig() {
        RecorderViewModel.getInstance().startTranslationConfig()
    }

    override fun getAllAsrContent(): List<ConvertContentItem> {
        return RecorderViewModel.getInstance().getAllAsrContent()
    }

    override fun getRealtimeAsrStatus(): RealTimeAsrStatus {
        return RecorderViewModel.getInstance().getRealtimeAsrStatus()
    }

    override fun externalInitAsr() {
        RecorderViewModel.getInstance().externalInitAsr()
    }

    override fun externalStopAsr() {
        RecorderViewModel.getInstance().externalStopAsr()
    }

    override fun updateSpeakerName(roleId: Int, newName: String): Boolean {
        return RecorderViewModel.getInstance().updateSpeakerName(roleId, newName)
    }

    override fun getRealtimeSubtitleCache(): IRealtimeSubtitleCache? {
        return RecorderViewModel.getInstance().getRealtimeSubtitleCache()
    }

    override fun getCurSelectedLanguage(): String {
        return RecorderViewModel.getInstance().curSelectedLanguage
    }

    override fun setCurSelectedLanguage(language: String) {
        RecorderViewModel.getInstance().curSelectedLanguage = language
    }

    override fun getSupportLanguageListFromServer(callback: IGetSupportLanguage) {
        RecorderViewModel.getInstance().getSupportLanguageListFromServer(callback)
    }

    override fun getLangListFromCatchOrSp(): List<String>? = RecorderViewModel.getInstance().getSupportLanguageListFromCatchOrSp()

    override fun isRealTimeSwitch(): Boolean {
        return RecorderViewModel.getInstance().isRealTimeSwitch()
    }

    override fun setRealTimeSwitch(mRealTimeSwitch: Boolean) {
        RecorderViewModel.getInstance().setRealTimeSwitch(mRealTimeSwitch)
    }

    override fun getRealTimeSubtitleBuryingPointCallback(callback: IGetBuryingPointRealTimeSubtitleInstance) {
        RecorderViewModel.getInstance().getRealTimeSubtitleBuryingPoint()?.let { callback.setInstance(it) }
    }

    override fun getSpeechLanguage(): String {
        return RecorderViewModel.getInstance().getSpeechLanguage()
    }

    override fun setSpeechLanguage(language: String) {
        RecorderViewModel.getInstance().setSpeechLanguage(language)
    }
}