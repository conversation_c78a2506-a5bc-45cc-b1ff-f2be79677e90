/*
 Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 File: RecorderViewModelApiTest
 Description:
 Version: 1.0
 Date: 2023/05/25 1.0
 Author: W9010241
 -----------Revision History-----------
 <author> <date> <version> <desc>
 W9010241 2023/05/25 1.0 create
 */

package com.soundrecorder.recorderservice.api

import android.content.Context
import android.net.Uri
import android.os.Build
import androidx.lifecycle.MutableLiveData
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.common.constant.RecordModeConstant
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.modulerouter.convertService.listener.RecorderControllerListener
import com.soundrecorder.modulerouter.recorder.RecorderDataConstant
import com.soundrecorder.recorderservice.shadows.ShadowFeatureOption
import com.soundrecorder.recorderservice.shadows.ShadowOplusUsbEnvironment
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowFeatureOption::class, ShadowOplusUsbEnvironment::class]
)
class RecorderViewModelApiTest {

    private var context: Context? = null
    private var mockApplication: MockedStatic<BaseApplication>? = null

    @Before
    fun setUp() {
        context = ApplicationProvider.getApplicationContext()
        mockApplication = Mockito.mockStatic(BaseApplication::class.java)
        mockApplication?.`when`<Context> { BaseApplication.getApplication() }?.thenReturn(context)
        mockApplication?.`when`<Context> { BaseApplication.getApplication() }?.thenReturn(context)
    }

    @After
    fun tearDown() {
        mockApplication?.close()
        mockApplication = null
        context = null
    }

    @Test
    fun should_contains_when_startRecorderService() {
        RecorderViewModelApi.startRecorderService {
            this.putExtra(RecorderDataConstant.RECORDER_TYPE, 1)
        }
    }

    @Test
    fun should_contains_when_test() {
        val list = RecorderViewModelApi.getMarkData()
        Assert.assertTrue(list.isEmpty())
        val lastTime = RecorderViewModelApi.getLastMarkTime()
        Assert.assertTrue(lastTime == 0L)
        val recordType = RecorderViewModelApi.getRecordType()
        Assert.assertEquals(RecordModeConstant.RECORD_TYPE_STANDARD, recordType)
        val amplitudeList = RecorderViewModelApi.getAmplitudeList()
        Assert.assertTrue(amplitudeList.isEmpty())
        val lastAmplitude = RecorderViewModelApi.getLatestAmplitude()
        Assert.assertTrue(lastAmplitude == 0)
        val maxAmplitude = RecorderViewModelApi.getMaxAmplitude()
        Assert.assertTrue(maxAmplitude == 0)
        val isMarkEnabledFull = RecorderViewModelApi.isMarkEnabledFull()
        Assert.assertTrue(isMarkEnabledFull)
        val checkMarkDataMoreThanMax = RecorderViewModelApi.checkMarkDataMoreThanMax()
        Assert.assertFalse(checkMarkDataMoreThanMax)
        val isNeedShowNotificationPermissionDeniedSnackBar =
            RecorderViewModelApi.isNeedShowNotificationPermissionDeniedSnackBar()
        Assert.assertFalse(isNeedShowNotificationPermissionDeniedSnackBar)
        RecorderViewModelApi.resetNeedShowNotificationPermissionDeniedSnackBar()
        Assert.assertFalse(RecorderViewModelApi.isAlreadyRecording())
    }

    @Test
    fun should_contains_when_play() {
        RecorderViewModelApi.start()
        RecorderViewModelApi.resume()
        RecorderViewModelApi.pause()
        RecorderViewModelApi.stop()
        RecorderViewModelApi.cancelRecordNotification()
    }

    @Test
    fun should_contains_when_getSampleUri() {
        val uri: Uri? = RecorderViewModelApi.getSampleUri()
        Assert.assertNull(uri)
    }

    @Test
    fun should_contains_when_getSuffix() {
        val suffix = RecorderViewModelApi.getSuffix()
        Assert.assertNull(suffix)
    }

    @Test
    fun should_contains_when_getRecordFilePath() {
        val filePath = RecorderViewModelApi.getRecordFilePath()
        Assert.assertNull(filePath)
    }

    @Test
    fun should_contains_when_getRelativePath() {
        val filePath = RecorderViewModelApi.getRelativePath()
        Assert.assertNull(filePath)
    }

    @Test
    fun should_contains_when_getDefaultDisplayName() {
        val displayName = RecorderViewModelApi.getSampleDisplayName()
        Assert.assertEquals(displayName, "")
    }

    @Test
    fun should_contains_when_getFileBeingRecorded() {
        val fileBeing = RecorderViewModelApi.getFileBeingRecorded()
        Assert.assertNull(fileBeing)
    }

    @Test
    fun should_contains_when_isQuickRecord() {
        val quick = RecorderViewModelApi.isQuickRecord()
        Assert.assertTrue(quick)
    }

    @Test
    fun should_contains_when_saveRecordInfo() {
        val displayName = "测试"
        val originalDisplayName = "测试1"
        val saveRecordFromWhere = 1
        val needStopService = false
        RecorderViewModelApi.saveRecordInfo(displayName, originalDisplayName, saveRecordFromWhere, needStopService)
    }

    @Test
    fun should_contains_when_hasInitRecorderService() {
        val hasInit = RecorderViewModelApi.hasInitRecorderService()
        Assert.assertFalse(hasInit)
    }

    @Test
    fun should_contains_when_stopService() {
        RecorderViewModelApi.stopService()
        RecorderViewModelApi.switchRecorderStatus("")
    }

    @Test
    fun should_contains_when_addMark() {
        val mark = Mockito.mock(MarkMetaData::class.java)
        RecorderViewModelApi.addMark(mark)
    }

    @Test
    fun should_contains_when_addMultiPictureMark() {
        val mark1 = Mockito.mock(MarkMetaData::class.java)
        val mark2 = Mockito.mock(MarkMetaData::class.java)
        val list = ArrayList<MarkMetaData>()
        list.add(mark1)
        list.add(mark2)
        val key = RecorderViewModelApi.addMultiPictureMark(list)
        Assert.assertTrue(key == -1)
    }

    @Test
    fun should_contains_when_removeMark() {
        val mark1 = Mockito.mock(MarkMetaData::class.java)
        val list = ArrayList<MarkMetaData>()
        list.add(mark1)
        RecorderViewModelApi.removeMark(0)
    }

    @Test
    fun should_contains_when_renameMark() {
        val newText = "测试1"
        val success = RecorderViewModelApi.renameMark(newText, "", 0)
        Assert.assertFalse(success)
    }

    @Test
    fun should_contains_when_isFromSlidBar() {
        val isFromSlidBar = RecorderViewModelApi.isFromSlidBar()
        Assert.assertFalse(isFromSlidBar)
    }

    @Test
    fun should_contains_when_isFromSmallCard() {
        val isFromSmallCard = RecorderViewModelApi.isFromSmallCard()
        Assert.assertFalse(isFromSmallCard)
    }

    @Test
    fun should_contains_when_isFromOtherApp() {
        val isFromOtherApp = RecorderViewModelApi.isFromOtherApp()
        Assert.assertFalse(isFromOtherApp)
    }

    @Test
    fun should_contains_when_isFromBreno() {
        val isFromBreno = RecorderViewModelApi.isFromBreno()
        Assert.assertFalse(isFromBreno)
    }

    @Test
    fun should_contains_when_addSourceForNotificationBtnDisabled() {
        val mutableLiveData = MutableLiveData(false)
        RecorderViewModelApi.addSourceForNotificationBtnDisabled(mutableLiveData)
    }

    @Test
    fun should_contains_when_addListener() {
        val listener = Mockito.mock(RecorderControllerListener::class.java)
        RecorderViewModelApi.addListener(listener)
    }

    @Test
    fun should_contains_when_removeListener() {
        val listener = Mockito.mock(RecorderControllerListener::class.java)
        RecorderViewModelApi.removeListener(listener)
    }

    @Test
    fun should_contains_when_isStartServiceFromOtherApp() {
        val isStartServiceFromOtherApp = RecorderViewModelApi.isStartServiceFromOtherApp()
        Assert.assertFalse(isStartServiceFromOtherApp)
    }

    @Test
    fun should_contains_when_getCurrentStatus() {
        val status = RecorderViewModelApi.getCurrentStatus()
        Assert.assertTrue(status == -1)
    }

    @Test
    fun should_contains_when_getLastStatus() {
        val status = RecorderViewModelApi.getLastStatus()
        Assert.assertTrue(status == -2)
    }

    @Test
    fun should_contains_when_checkModeCanRecord() {
        val check = RecorderViewModelApi.checkModeCanRecord(false)
        Assert.assertTrue(check)
    }

    @Test
    fun should_contains_when_isAudioModeChangePause() {
        val check = RecorderViewModelApi.isAudioModeChangePause()
        Assert.assertFalse(check)
    }

    @Test
    fun should_contains_when_isNeedResume() {
        val check = RecorderViewModelApi.isNeedResume()
        Assert.assertFalse(check)
    }

    @Test
    fun should_contains_when_checkDistBeforeStartRecord() {
        val check = RecorderViewModelApi.checkDistBeforeStartRecord()
        Assert.assertFalse(check)
    }

    @Test
    fun should_contains_when_hasInitAmplitude() {
        val check = RecorderViewModelApi.hasInitAmplitude()
        Assert.assertFalse(check)
    }

    @Test
    fun should_contains_when_getAmplitudeCurrentTime() {
        val time = RecorderViewModelApi.getAmplitudeCurrentTime()
        Assert.assertTrue(time == 0L)
    }
}