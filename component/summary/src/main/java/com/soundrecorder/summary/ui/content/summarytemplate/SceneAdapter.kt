/*********************************************************************
 * * Copyright (C), 2025, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  SceneAdapter
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/07/08
 * * Author      : w9099028
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.summary.ui.content.summarytemplate

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.tagview.COUITagView
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.common.drawable.SweepGradientWithShapeLayer
import com.soundrecorder.summary.model.SummaryTheme
import com.soundrecorder.summary.ui.TagContainer

class SceneAdapter(
    private val context: Context
) : RecyclerView.Adapter<SceneAdapter.ViewHolder>() {
    private var sceneList: List<SceneItem> = emptyList()
    private var itemClickListener: ((Int) -> Unit)? = null
    private var selectedPosition = -1

    private val selectedBg by lazy {
        SweepGradientWithShapeLayer(
            context,
            context.resources.getDimension(com.soundrecorder.common.R.dimen.dp16),
            context.resources.getColor(com.soundrecorder.summary.R.color.select_summary_style_item_bg_color)
        )
    }
    private val defaultBg by lazy {
        ContextCompat.getDrawable(context, com.soundrecorder.summary.R.drawable.bg_scene_item)
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val title: TextView = itemView.findViewById(com.soundrecorder.summary.R.id.scene_title)
        val icon: ImageView = itemView.findViewById(com.soundrecorder.summary.R.id.scene_icon)
        val chipGroup: TagContainer = itemView.findViewById(com.soundrecorder.summary.R.id.scene_chip_group)

        init {
            itemView.setOnClickListener {
                val position = absoluteAdapterPosition
                // 使用info级别日志确保能够输出，并添加更多调试信息
                DebugUtil.i("SceneAdapter", "itemView clicked, absoluteAdapterPosition = $position, itemClickListener = ${itemClickListener != null}")

                if (position != RecyclerView.NO_POSITION && position >= 0) {
                    DebugUtil.i("SceneAdapter", "valid position, invoking itemClickListener for position = $position")
                    itemClickListener?.invoke(position)
                    // 更新选中位置
                    setSelectedPosition(position)
                } else {
                    DebugUtil.w("SceneAdapter", "invalid position: $position, click ignored")
                }
            }
        }
    }

    fun setSelectedPosition(position: Int) {
        val oldPosition = selectedPosition
        selectedPosition = position
        // 使用info级别日志确保能够输出
        DebugUtil.i("SceneAdapter", "setSelectedPosition: oldPosition = $oldPosition, newPosition = $selectedPosition")
        if (oldPosition != -1) {
            DebugUtil.d("SceneAdapter", "notifying item changed for old position: $oldPosition")
            notifyItemChanged(oldPosition)
        }
        if (position != -1) {
            DebugUtil.d("SceneAdapter", "notifying item changed for new position: $position")
            notifyItemChanged(position)
        }
    }

    fun getItemAt(position: Int): SceneItem? {
        return sceneList.getOrNull(position)
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(com.soundrecorder.summary.R.layout.item_scene, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = sceneList[position]
        holder.title.text = item.title
        holder.itemView.background = if (position == selectedPosition) {
            selectedBg
        } else {
            defaultBg
        }

        val iconRes = when (item.id) {
            SummaryTheme.NORMAL -> com.soundrecorder.summary.R.drawable.ic_normal
            SummaryTheme.MEETING -> com.soundrecorder.summary.R.drawable.ic_meeting
            SummaryTheme.PHONE -> com.soundrecorder.summary.R.drawable.ic_phone
            SummaryTheme.INTERVIEW -> com.soundrecorder.summary.R.drawable.ic_interview
            SummaryTheme.CLASSROOM -> com.soundrecorder.summary.R.drawable.ic_classroom
            else -> null
        }
        iconRes?.let {
            holder.icon.setImageResource(it)
        } ?: holder.icon.setImageDrawable(null)

        holder.chipGroup.removeAllViews()
        val chipTexts = getTagForTheme(holder.itemView.context, item.id)
        addTagsToGroup(holder.chipGroup, chipTexts)
    }

    private fun getTagForTheme(context: Context, themeId: Int): Array<String> {
        return when (themeId) {
            SummaryTheme.NORMAL -> context.resources.getStringArray(com.soundrecorder.summary.R.array.standard_action_chip)
            SummaryTheme.MEETING -> context.resources.getStringArray(com.soundrecorder.summary.R.array.meeting_action_chip)
            SummaryTheme.PHONE -> context.resources.getStringArray(com.soundrecorder.summary.R.array.call_action_chip)
            SummaryTheme.INTERVIEW -> context.resources.getStringArray(com.soundrecorder.summary.R.array.interviews_action_chip)
            SummaryTheme.CLASSROOM -> context.resources.getStringArray(com.soundrecorder.summary.R.array.notes_action_chip)
            else -> emptyArray()
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun addTagsToGroup(group: TagContainer, texts: Array<String>) {
        texts.forEach { text ->
            val chip = (LayoutInflater.from(group.context).inflate(
                com.soundrecorder.summary.R.layout.item_chip_scene_suggestion,
                group,
                false
            ) as? COUITagView)?.apply {
                this.setTagText(text)
                isClickable = false
                isFocusable = false
                isFocusableInTouchMode = false
                isDuplicateParentStateEnabled = false
                // 移除触摸事件拦截，让父容器能正常处理点击事件
                // 原来的 setOnTouchListener { _, _ -> true } 会拦截所有触摸事件
                // 改为不设置OnTouchListener，让事件正常传递
            }
            group.addView(chip)
        }
    }

    override fun getItemCount(): Int = sceneList.size

    fun setData(list: List<SceneItem>, selectedPos: Int = -1) {
        DebugUtil.i("SceneAdapter", "setData: list size = ${list.size}, selectedPos = $selectedPos")
        sceneList = list
        setSelectedPosition(selectedPos)
        notifyDataSetChanged()
    }

    fun setOnItemClickListener(listener: (Int) -> Unit) {
        DebugUtil.i("SceneAdapter", "setOnItemClickListener: listener set = ${listener != null}")
        itemClickListener = listener
    }
}

data class SceneItem(
    val id: Int,
    val title: String,
    val iconRes: Int
)