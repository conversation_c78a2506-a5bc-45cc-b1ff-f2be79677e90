/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  AutoDiForWaveMark.kt
 * * Description : AutoDiForWaveMark
 * * Version     : 1.0
 * * Date        : 2025/04/18
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *    renjiahao                             1.0        create
 ***********************************************************************/
package com.soundrecorder.wavemark.di

import com.soundrecorder.modulerouter.waveMark.WaveMarkInterface
import com.soundrecorder.wavemark.WaveMarkApi
import org.koin.dsl.module

object AutoDiForWaveMark {
    val waveMarkModule = module {
        single<WaveMarkInterface>(createdAtStart = true) {
            WaveMarkApi
        }
    }
}