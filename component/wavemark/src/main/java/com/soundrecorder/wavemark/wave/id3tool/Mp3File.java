/*
 * Copyright (C) 2010 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.soundrecorder.wavemark.wave.id3tool;

import android.content.Context;
import android.net.Uri;

import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.common.constant.Constants;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class Mp3File extends FileWrapper {
    private static final String TAG = "Mp3File";
    private static final int DEFAULT_BUFFER_LENGTH = 65536;
    private static final int MINIMUM_BUFFER_LENGTH = 40;
    private static final int XING_MARKER_OFFSET_1 = 13;
    private static final int XING_MARKER_OFFSET_2 = 21;
    private static final int XING_MARKER_OFFSET_3 = 36;
    private static final int ZERO = 0;
    private static final int ONE = 1;
    private static final int TWO = 2;
    private static final int THREE = 3;
    private static final int FOUR = 4;
    private static final int FIVE = 5;
    private static final int SIX = 6;
    private static final int SEVEN = 7;
    private static final int TWELVE = 12;
    protected int bufferLength;
    private int xingOffset = -1;
    private int startOffset = -1;
    private int endOffset = -1;
    private int frameCount = 0;
    private Map<Integer, MutableInteger> bitrates = new HashMap<>();
    private int xingBitrate;
    private double bitrate = 0;
    private String channelMode;
    private String emphasis;
    private String layer;
    private String modeExtension;
    private int sampleRate;
    private boolean copyright;
    private boolean original;
    private String version;
    private ID3v1 id3v1Tag;
    private ID3v2 id3v2Tag;
    private String mHeader;
    private byte[] customTag;
    private byte[] mMarkSizeBytes = new byte[FOUR];
    private boolean scanFile;
    private int lastFrameLength;
    private long markStrSize;
    private volatile boolean cancleScanFlag = false;
    private FileChannel mFileChannel = null;
    private FileInputStream mFileInputStream = null;
    private FileOutputStream mSaveFileOutputStream = null;

    public Mp3File() {
    }

    public Mp3File(String filename) throws IOException, UnsupportedTagException, InvalidDataException {
        super(filename);
    }

    public Mp3File(File file, int bufferLength, boolean scanFile) throws IOException, UnsupportedTagException, InvalidDataException {
        super(file);
        this.bufferLength = bufferLength;
        this.scanFile = scanFile;
    }

    public Mp3File(File file) throws IOException, UnsupportedTagException, InvalidDataException {
        this(file, DEFAULT_BUFFER_LENGTH, true);
        mFileInputStream = new FileInputStream(file);
        mFileChannel = mFileInputStream.getChannel();
    }

    public void create(Uri uri, Context context) throws FileNotFoundException {
        mFileInputStream = (FileInputStream) context.getContentResolver().openInputStream(uri);
        if (mFileInputStream != null) {
            mFileChannel = mFileInputStream.getChannel();
        }
        this.bufferLength = DEFAULT_BUFFER_LENGTH;
    }

    public void setCancleScanFlag(boolean cancleScanFlag) {
        this.cancleScanFlag = cancleScanFlag;
    }

    private void init(int bufferLength, boolean scanFile) throws IOException, UnsupportedTagException, InvalidDataException {
        if (bufferLength < MINIMUM_BUFFER_LENGTH + 1) {
            throw new IllegalArgumentException("Buffer too small");
        }
        if (mFileChannel == null) {
            DebugUtil.i(TAG, "init mFileChannel is null.");
            return;
        }
        initId3v1Tag(mFileChannel);
        scanFile(mFileChannel);
        if (startOffset < 0) {
            throw new InvalidDataException("No mpegs frames found");
        }
        initId3v2Tag(mFileChannel);
        if (scanFile) {
            initCustomTag(mFileChannel);
        }
    }

    public void extractMp3FileWithDefault(boolean scanFile) throws IOException, UnsupportedTagException, InvalidDataException {
        extractMp3File(DEFAULT_BUFFER_LENGTH, scanFile);
    }


    public void extractMp3File(int bufferLength, boolean scanFile) throws IOException, UnsupportedTagException, InvalidDataException {
        if (bufferLength < MINIMUM_BUFFER_LENGTH + 1) {
            throw new IllegalArgumentException("Buffer too small");
        }

        this.bufferLength = bufferLength;
        this.scanFile = scanFile;

        initMarkStrTag(mFileChannel);
    }

    protected int preScanFile(FileChannel seekableByteChannel) {
        ByteBuffer byteBuffer = ByteBuffer.allocate(AbstractID3v2Tag.HEADER_LENGTH);
        try {
            seekableByteChannel.position(0);
            byteBuffer.clear();
            int bytesRead = seekableByteChannel.read(byteBuffer);
            if (bytesRead == AbstractID3v2Tag.HEADER_LENGTH) {
                try {
                    byte[] bytes = byteBuffer.array();
                    ID3v2TagFactory.sanityCheckTag(bytes);
                    return AbstractID3v2Tag.HEADER_LENGTH
                            + BufferTools.unpackSynchsafeInteger(bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET],
                            bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET + ONE],
                            bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET + TWO],
                            bytes[AbstractID3v2Tag.DATA_LENGTH_OFFSET + THREE]);
                } catch (NoSuchTagException | UnsupportedTagException e) {
                    // do nothing
                }
            }
        } catch (IOException e) {
            // do nothing
        }
        return 0;
    }

    private void scanFile(FileChannel seekableByteChannel) throws IOException, InvalidDataException {
        ByteBuffer byteBuffer = ByteBuffer.allocate(bufferLength);
        int fileOffset = preScanFile(seekableByteChannel);
        seekableByteChannel.position(fileOffset);
        boolean lastBlock = false;
        int lastOffset = fileOffset;
        while (!lastBlock) {
            byteBuffer.clear();
            int bytesRead = seekableByteChannel.read(byteBuffer);
            byte[] bytes = byteBuffer.array();
            if (bytesRead < bufferLength) {
                lastBlock = true;
            }
            if (bytesRead >= MINIMUM_BUFFER_LENGTH) {
                while (true) {
                    try {
                        if (cancleScanFlag) {
                            startOffset = -1;
                            xingOffset = -1;
                            frameCount = 0;
                            bitrates.clear();
                            lastBlock = false;
                            fileOffset = lastOffset + 1;
                            if (fileOffset == 0) {
                                throw new InvalidDataException("Valid start of mpeg frames not found");
                            }
                            seekableByteChannel.position(fileOffset);
                            DebugUtil.i(TAG, "cancel scan return");
                            return;
                        }
                        int offset = 0;
                        if (startOffset < 0) {
                            offset = scanBlockForStart(bytes, bytesRead, fileOffset, offset);
                            if (startOffset >= 0 && !scanFile) {
                                return;
                            }
                            lastOffset = startOffset;
                        }
                        offset = scanBlock(bytes, bytesRead, fileOffset, offset);
                        fileOffset += offset;
                        seekableByteChannel.position(fileOffset);
                        break;
                    } catch (Exception e) {
                        DebugUtil.d(TAG, "scanFile is error , the e is " + e);
                        if (frameCount < 2) {
                            startOffset = -1;
                            xingOffset = -1;
                            frameCount = 0;
                            bitrates.clear();
                            lastBlock = false;
                            fileOffset = lastOffset + 1;
                            if (fileOffset == 0) {
                                throw new InvalidDataException("Valid start of mpeg frames not found", e);
                            }
                            seekableByteChannel.position(fileOffset);
                            break;
                        }
                        return;
                    }
                }
            }
        }
    }

    private int scanBlockForStart(byte[] bytes, int bytesRead, int absoluteOffset, int offset) {
        while (offset < bytesRead - MINIMUM_BUFFER_LENGTH) {
            if (bytes[offset] == (byte) 0xFF && (bytes[offset + 1] & (byte) 0xE0) == (byte) 0xE0) {
                try {
                    MpegFrame frame = new MpegFrame(bytes[offset], bytes[offset + ONE], bytes[offset + TWO], bytes[offset + THREE]);
                    if (xingOffset < 0 && isXingFrame(bytes, offset)) {
                        xingOffset = absoluteOffset + offset;
                        xingBitrate = frame.getBitrate();
                        offset += frame.getLengthInBytes();
                    } else {
                        startOffset = absoluteOffset + offset;
                        channelMode = frame.getChannelMode();
                        emphasis = frame.getEmphasis();
                        layer = frame.getLayer();
                        modeExtension = frame.getModeExtension();
                        sampleRate = frame.getSampleRate();
                        version = frame.getVersion();
                        copyright = frame.isCopyright();
                        original = frame.isOriginal();
                        frameCount++;
                        addBitrate(frame.getBitrate());
                        offset += frame.getLengthInBytes();
                        return offset;
                    }
                } catch (InvalidDataException e) {
                    offset++;
                }
            } else {
                offset++;
            }
        }
        return offset;
    }

    private int scanBlock(byte[] bytes, int bytesRead, int absoluteOffset, int offset) throws InvalidDataException {
        while (offset < bytesRead - MINIMUM_BUFFER_LENGTH) {
            MpegFrame frame = null;
            try {
                frame = new MpegFrame(bytes[offset], bytes[offset + ONE], bytes[offset + TWO], bytes[offset + THREE]);
                sanityCheckFrame(frame, absoluteOffset + offset);

                int newEndOffset = absoluteOffset + offset + frame.getLengthInBytes() - 1;
                if (newEndOffset < maxEndOffset()) {
                    endOffset = absoluteOffset + offset + frame.getLengthInBytes() - 1;
                    frameCount++;
                    addBitrate(frame.getBitrate());
                    offset += frame.getLengthInBytes();
                    lastFrameLength = frame.getLengthInBytes();
                } else {
                    break;
                }
            } catch (Exception e) {
                int newEndOffset = absoluteOffset + offset + lastFrameLength - 1;
                if (newEndOffset < maxEndOffset()) {
                    offset += 4;
                } else {
                    break;
                }
            }
        }
        return offset;
    }

    private int maxEndOffset() {
        int maxEndOffset = 0;
        try {
            maxEndOffset = (int) mFileChannel.size();
        } catch (IOException e) {
            e.printStackTrace();
        }
        if (hasId3v1Tag()) {
            maxEndOffset -= ID3v1Tag.TAG_LENGTH;
        }
        return maxEndOffset;
    }

    private boolean isXingFrame(byte[] bytes, int offset) {
        if (bytes.length >= offset + XING_MARKER_OFFSET_1 + THREE) {
            if ("Xing".equals(BufferTools.byteBufferToStringIgnoringEncodingIssues(bytes, offset + XING_MARKER_OFFSET_1, FOUR))) {
                return true;
            }
            if ("Info".equals(BufferTools.byteBufferToStringIgnoringEncodingIssues(bytes, offset + XING_MARKER_OFFSET_1, FOUR))) {
                return true;
            }
            if (bytes.length >= offset + XING_MARKER_OFFSET_2 + THREE) {
                if ("Xing".equals(BufferTools.byteBufferToStringIgnoringEncodingIssues(bytes, offset + XING_MARKER_OFFSET_2, FOUR))) {
                    return true;
                }
                if ("Info".equals(BufferTools.byteBufferToStringIgnoringEncodingIssues(bytes, offset + XING_MARKER_OFFSET_2, FOUR))) {
                    return true;
                }
                if (bytes.length >= offset + XING_MARKER_OFFSET_3 + THREE) {
                    if ("Xing".equals(BufferTools.byteBufferToStringIgnoringEncodingIssues(bytes, offset + XING_MARKER_OFFSET_3, FOUR))) {
                        return true;
                    }
                    if ("Info".equals(BufferTools.byteBufferToStringIgnoringEncodingIssues(bytes, offset + XING_MARKER_OFFSET_3, FOUR))) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    private void sanityCheckFrame(MpegFrame frame, int offset) throws InvalidDataException, IOException {
        if (sampleRate != frame.getSampleRate()) {
            throw new InvalidDataException("Inconsistent frame header");
        }
        if (!layer.equals(frame.getLayer())) {
            throw new InvalidDataException("Inconsistent frame header");
        }
        if (!version.equals(frame.getVersion())) {
            throw new InvalidDataException("Inconsistent frame header");
        }
        if (offset + frame.getLengthInBytes() > mFileChannel.size()) {
            throw new InvalidDataException("Frame would extend beyond end of file");
        }
    }

    private void addBitrate(int bitrate) {
        Integer key = new Integer(bitrate);
        MutableInteger count = bitrates.get(key);
        if (count != null) {
            count.increment();
        } else {
            bitrates.put(key, new MutableInteger(1));
        }
        this.bitrate = ((this.bitrate * (frameCount - 1)) + bitrate) / frameCount;
    }

    private void initId3v1Tag(FileChannel seekableByteChannel) throws IOException {
        ByteBuffer byteBuffer = ByteBuffer.allocate(ID3v1Tag.TAG_LENGTH);
        seekableByteChannel.position(seekableByteChannel.size() - ID3v1Tag.TAG_LENGTH);
        byteBuffer.clear();
        int bytesRead = seekableByteChannel.read(byteBuffer);
        if (bytesRead < ID3v1Tag.TAG_LENGTH) {
            throw new IOException("Not enough bytes read");
        }
        try {
            id3v1Tag = new ID3v1Tag(byteBuffer.array());
        } catch (NoSuchTagException e) {
            id3v1Tag = null;
        }
    }

    private boolean initMarkSizeTag(FileChannel seekableByteChannel) throws IOException {
        if (seekableByteChannel == null) {
            DebugUtil.e(TAG, "initMarkSizeTag seekableByteChannel is null.");
            return false;
        }
        ByteBuffer byteBuffer = ByteBuffer.allocate(TWELVE);
        seekableByteChannel.position(seekableByteChannel.size() - ID3v1Tag.TAG_LENGTH - TWELVE);

        byteBuffer.clear();
        int bytesRead = seekableByteChannel.read(byteBuffer);
        if (bytesRead < TWELVE) {
            throw new IOException("Not enough bytes read");
        }
        try {
            byte[] tempBytes = byteBuffer.array();
            String tempStr = new String(tempBytes, Constants.UTF_8);
            DebugUtil.d(TAG, "the tempStr is " + tempStr + ", the tempBytes is " + Arrays.toString(tempBytes));
            if ((tempStr.startsWith("mark")) && (tempStr.endsWith("mark"))) {
                mMarkSizeBytes[ZERO] = tempBytes[FOUR];
                mMarkSizeBytes[ONE] = tempBytes[FIVE];
                mMarkSizeBytes[TWO] = tempBytes[SIX];
                mMarkSizeBytes[THREE] = tempBytes[SEVEN];
                DebugUtil.d(TAG, "initMarkSizeTag, the mMarkSizeBytes is " + Arrays.toString(mMarkSizeBytes));
                return true;
            } else {
                //the mp3 file is come from clone phone
                return false;
            }
        } catch (Exception e) {
            mMarkSizeBytes = null;
            e.printStackTrace();
        }
        return false;
    }

    private void initMarkStrTag(FileChannel seekableByteChannel) throws IOException {
        try {
            if (initMarkSizeTag(seekableByteChannel)) {
                int markStrSize = byteArrayToInt(mMarkSizeBytes);
                ByteBuffer byteBuffer = ByteBuffer.allocate(markStrSize);
                long pos = seekableByteChannel.size() - ID3v1Tag.TAG_LENGTH - TWELVE - markStrSize;
                seekableByteChannel.position(pos);
                byteBuffer.clear();
                int bytesRead = seekableByteChannel.read(byteBuffer);

                if (bytesRead < TWELVE) {
                    throw new IOException("Not enough bytes read");
                }

                customTag = byteBuffer.array();
            } else {
                init(this.bufferLength, this.scanFile);
            }
        } catch (Exception e) {
            customTag = null;
            DebugUtil.e(TAG, "init mark string tag error", e);
        }
    }

    private void initId3v2Tag(FileChannel seekableByteChannel) throws IOException, UnsupportedTagException, InvalidDataException {
        if ((xingOffset == 0) || (startOffset == 0)) {
            id3v2Tag = null;
        } else {
            int bufferLength;
            if (hasXingFrame()) {
                bufferLength = xingOffset;
            } else {
                bufferLength = startOffset;
            }
            ByteBuffer byteBuffer = ByteBuffer.allocate(bufferLength);
            seekableByteChannel.position(0);
            byteBuffer.clear();
            int bytesRead = seekableByteChannel.read(byteBuffer);
            if (bytesRead < bufferLength) {
                throw new IOException("Not enough bytes read");
            }
            try {
                id3v2Tag = ID3v2TagFactory.createTag(byteBuffer.array());
            } catch (NoSuchTagException e) {
                id3v2Tag = null;
            }
        }
    }

    private void initCustomTag(FileChannel seekableByteChannel) throws IOException {
        int bufferLength = (int) (seekableByteChannel.size() - (endOffset + 1));
        if (hasId3v1Tag()) {
            bufferLength -= ID3v1Tag.TAG_LENGTH;
        }
        if (bufferLength <= 0) {
            customTag = null;
        } else {
            ByteBuffer byteBuffer = ByteBuffer.allocate(bufferLength);
            seekableByteChannel.position(endOffset + 1);
            byteBuffer.clear();
            int bytesRead = seekableByteChannel.read(byteBuffer);
            customTag = byteBuffer.array();
            DebugUtil.d(TAG, "initCustomTag, the endOffset is " + endOffset + ", bufferLength is " + bufferLength + ", customTag is " + Arrays.toString(customTag));
            if (bytesRead < bufferLength) {
                throw new IOException("Not enough bytes read");
            }
        }
    }

    public int getFrameCount() {
        return frameCount;
    }

    public int getStartOffset() {
        return startOffset;
    }

    public int getEndOffset() {
        return endOffset;
    }

    public long getLengthInMilliseconds() {
        double d = 8 * (endOffset - startOffset);
        return (long) ((d / bitrate) + 0.5);
    }

    public long getLengthInSeconds() {
        return ((getLengthInMilliseconds() + 500) / 1000);
    }

    public boolean isVbr() {
        return bitrates.size() > 1;
    }

    public int getBitrate() {
        return (int) (bitrate + 0.5);
    }

    public Map<Integer, MutableInteger> getBitrates() {
        return bitrates;
    }

    public String getChannelMode() {
        return channelMode;
    }

    public boolean isCopyright() {
        return copyright;
    }

    public String getEmphasis() {
        return emphasis;
    }

    public String getLayer() {
        return layer;
    }

    public String getModeExtension() {
        return modeExtension;
    }

    public boolean isOriginal() {
        return original;
    }

    public int getSampleRate() {
        return sampleRate;
    }

    public String getVersion() {
        return version;
    }

    public boolean hasXingFrame() {
        return (xingOffset >= 0);
    }

    public int getXingOffset() {
        return xingOffset;
    }

    public int getXingBitrate() {
        return xingBitrate;
    }

    public boolean hasId3v1Tag() {
        return id3v1Tag != null;
    }

    public ID3v1 getId3v1Tag() {
        return id3v1Tag;
    }

    public void setId3v1Tag(ID3v1 id3v1Tag) {
        this.id3v1Tag = id3v1Tag;
    }

    public void removeId3v1Tag() {
        this.id3v1Tag = null;
    }

    public boolean hasId3v2Tag() {
        return id3v2Tag != null;
    }

    public ID3v2 getId3v2Tag() {
        return id3v2Tag;
    }

    public void setId3v2Tag(ID3v2 id3v2Tag) {
        this.id3v2Tag = id3v2Tag;
    }

    public void removeId3v2Tag() {
        this.id3v2Tag = null;
    }

    public boolean hasCustomTag() {
        return customTag != null;
    }

    public boolean hasMarkSizeTag() {
        return mMarkSizeBytes != null;
    }

    public boolean hasHeader() {
        return mHeader != null;
    }

    public byte[] getCustomTag() {
        return customTag;
    }

    public void setCustomTag(byte[] customTag) {
        this.customTag = customTag;
        DebugUtil.d(TAG, "setCustomTag, the customTag is " + Arrays.toString(customTag) + ", the size is " + customTag.length);
    }

    public void setMarkSizeTag(byte[] size) {
        this.mMarkSizeBytes = size;
    }

    public void saveMarkToNewFile(Object save) throws IOException, NotSupportedException {
        FileChannel seekableByteChannel = null;
        FileChannel saveFile = null;
        try {
            if (save instanceof OutputStream) {
                mSaveFileOutputStream = (FileOutputStream) save;
                saveFile = mSaveFileOutputStream.getChannel();
            } else if (save instanceof File) {
                File file = (File) save;
                mSaveFileOutputStream = new FileOutputStream(file, true);
                saveFile = mSaveFileOutputStream.getChannel();
            } else {
                return;
            }
            seekableByteChannel = mFileChannel;
            DebugUtil.i(TAG, "seekableByteChannel length: " + seekableByteChannel.size());
            seekableByteChannel.position(seekableByteChannel.size() - ID3v1Tag.TAG_LENGTH);
            ByteBuffer lastByteBufferOfId3V1Tag = ByteBuffer.allocate(ID3v1Tag.TAG_LENGTH);
            int bytesRead = seekableByteChannel.read(lastByteBufferOfId3V1Tag);

            if (hasCustomTag()) {
                ByteBuffer byteBufferOfCustomTag = ByteBuffer.wrap(customTag);
                byteBufferOfCustomTag.rewind();
                saveFile.write(byteBufferOfCustomTag);
            }

            if (hasMarkSizeTag()) {
                ByteBuffer byteBufOfMarkStartFlag = ByteBuffer.wrap("mark".getBytes(Constants.UTF_8));
                byteBufOfMarkStartFlag.rewind();
                saveFile.write(byteBufOfMarkStartFlag);

                ByteBuffer byteBufferOfSizeTag = ByteBuffer.wrap(mMarkSizeBytes);
                byteBufferOfSizeTag.rewind();
                saveFile.write(byteBufferOfSizeTag);

                ByteBuffer byteBufOfMarkEndFlag = ByteBuffer.wrap("mark".getBytes(Constants.UTF_8));
                byteBufOfMarkEndFlag.rewind();
                saveFile.write(byteBufOfMarkEndFlag);
            }

            if (bytesRead < ID3v1Tag.TAG_LENGTH) {
                throw new IOException("Not enough bytes read");
            }

            lastByteBufferOfId3V1Tag.rewind();
            saveFile.write(lastByteBufferOfId3V1Tag);
            DebugUtil.i(TAG, "save Id3V1Tag end saveFile position: " + saveFile.position());
        } catch (Exception e) {
            DebugUtil.e(TAG, "save wave or mark error", e);
        } finally {
            if (mFileInputStream != null) {
                try {
                    mFileInputStream.close();
                } catch (IOException e) {
                    DebugUtil.e(TAG, "close file input stream error", e);
                }
            }
            if (seekableByteChannel != null) {
                try {
                    seekableByteChannel.close();
                } catch (Throwable e) {
                    DebugUtil.e(TAG, "seekable byte channel close error", e);
                }
            }
            if (saveFile != null) {
                try {
                    saveFile.close();
                } catch (Throwable e) {
                    DebugUtil.e(TAG, "save file channel close error", e);
                }
            }
            if (mSaveFileOutputStream != null) {
                try {
                    mSaveFileOutputStream.close();
                } catch (Throwable e) {
                    DebugUtil.e(TAG, "save file output stream close error", e);
                }
            }
        }
    }

    public static byte[] longToBytes(long x) {
        ByteBuffer buffer = ByteBuffer.allocate(8);
        buffer.putLong(0, x);
        return buffer.array();
    }

    public static long bytesToLong(byte[] bytes) {
        if (bytes != null) {
            ByteBuffer buffer = ByteBuffer.allocate(8);
            buffer.put(bytes, 0, bytes.length);
            buffer.flip();//need flip
            return buffer.getLong();
        } else {
            return 0;
        }
    }

    public static int byteArrayToInt(byte[] b) {
        return b[3] & 0xFF |
                (b[2] & 0xFF) << 8 |
                (b[1] & 0xFF) << 16 |
                (b[0] & 0xFF) << 24;
    }

    public static byte[] intToByteArray(int a) {
        return new byte[]{
                (byte) ((a >> 24) & 0xFF),
                (byte) ((a >> 16) & 0xFF),
                (byte) ((a >> 8) & 0xFF),
                (byte) (a & 0xFF)
        };
    }

    public void release() {
        if (mFileInputStream != null) {
            try {
                mFileInputStream.close();
            } catch (IOException e) {
                DebugUtil.e(TAG, "close file input stream error", e);
            }
        }
        if (mFileChannel != null) {
            try {
                mFileChannel.close();
            } catch (IOException e) {
                DebugUtil.e(TAG, "close file channel error", e);
            }
        }
    }
}
