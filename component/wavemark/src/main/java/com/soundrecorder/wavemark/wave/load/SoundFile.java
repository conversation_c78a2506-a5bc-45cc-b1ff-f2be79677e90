/*
 * Copyright (C) 2015 Google Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.soundrecorder.wavemark.wave.load;

import android.content.Context;
import android.media.MediaCodec;
import android.media.MediaExtractor;
import android.media.MediaFormat;
import android.net.Uri;
import android.os.ParcelFileDescriptor;
import android.os.SystemClock;
import android.text.TextUtils;
import android.util.Pair;

import com.soundrecorder.base.utils.DebugUtil;
import com.soundrecorder.base.utils.FileUtils;
import com.soundrecorder.common.db.MediaDBUtils;

import java.io.File;
import java.io.FileDescriptor;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.ShortBuffer;
import java.util.Arrays;
import java.util.List;

public class SoundFile {
    private static final String TAG = "SoundFile";
    // Member variables representing frame data
    public static final int MAX_PROGRESS = 100;
    public static final int DEFAULT_PROGRESS = 10;
    private String mFileType;
    private int mFileSize;
    private int mSampleRate;
    private int mChannels;
    private int mNumSamples;  // total number of samples per channel in audio file
    private ByteBuffer mDecodedBytes;  // Raw audio data
    private ShortBuffer mDecodedSamples;  // shared buffer with mDecodedBytes.

    // Member variables for hack (making it work with old version, until app just uses the samples).
    private int mNumFrames;
    private int mDecodedSampleTotalSize;
    private List<Integer> mAmplitudeList;
    private boolean mStopDecoded = false;
    private DecodedSoundFileListener mSoundFileListener = null;
    private long mDuration;
    private float mOneWaveLineTime = 0F;
    private int mNeedWaveLineCount = 0;
    private int mExpectedNumSamples;

    private DecodedReadyListener mDecodedReadyListener = null;
    private int mWaitAmplitudes = 0;
    private boolean mReady = false;
    private boolean isDecodeFinish = false;
    private ProgressCallback mProgressCallback;
    private int mLastProgress = 0;
    private int mProgress = 0;
    private ParcelFileDescriptor mPfd = null;

    public boolean isDecodeFinish() {
        return isDecodeFinish;
    }

    public void setDecodeFinish(boolean decodeFinish) {
        isDecodeFinish = decodeFinish;
    }

    public void setWaitAmplitudes(int mWaitAmplitudes, DecodedReadyListener mDecodedReadyListener) {
        this.mWaitAmplitudes = mWaitAmplitudes;
        this.mDecodedReadyListener = mDecodedReadyListener;
    }

    public interface DecodedReadyListener {
        void onReady();
    }

    public interface DecodedSoundFileListener {
        boolean decodedSoundFileFinish(boolean isFinish);
    }

    public void setDecodedSoundFileListener(DecodedSoundFileListener listener) {
        mSoundFileListener = listener;
    }

    public void setStopDecoded(boolean stopDecoded) {
        mStopDecoded = stopDecoded;
    }

    public void setOneWaveLineTime(float time) {
        mOneWaveLineTime = time;
    }

    // Create and return a SoundFile object using the file fileName.
    public void create(String fileName, Object obj, Context context)
            throws java.io.FileNotFoundException, java.io.IOException {
        // First check that the file exists and that its extension is supported.
        mFileType = getSuffix(fileName);
        if (TextUtils.isEmpty(mFileType)) {
            return;
        }

        if (!Arrays.asList(getSupportedExtensions()).contains(mFileType)) {
            return;
        }
        mAmplitudeList = new WaveSafeArrayList();
        if (obj == null) {
            File file = new File(fileName);
            if (!file.exists()) {
                return;
            }
            readFile(file, context);
        } else if (obj instanceof Uri) {
            Uri uri = (Uri) obj;
            readFile(uri, context);
        }
    }

    private String getSuffix(String name) {
        String suffix = "";
        if (name.contains(".")) {
            int lastDot = name.lastIndexOf(".");
            if (name.length() > (lastDot + 1)) {
                suffix = name.substring(lastDot + 1);
            }
        }
        return suffix;
    }

    // what is the real list of supported extensions? Is it device dependent?
    public static String[] getSupportedExtensions() {
        return new String[]{"mp3", "wav", "3gpp", "3gp", "amr", "aac", "m4a", "ogg", "awb"};
    }

    public static boolean isFilenameSupported(String filename) {
        String[] extensions = getSupportedExtensions();

        for (int i = 0; i < extensions.length; i++) {
            if (filename.endsWith("." + extensions[i])) {
                return true;
            }
        }

        return false;
    }

    public int getSamplesPerFrame() {
        return (int) ((mExpectedNumSamples / 2) / mNeedWaveLineCount);
    }

    public List<Integer> getAmplitudeList() {
        return mAmplitudeList;
    }

    private void readFile(Object obj, Context context) throws FileNotFoundException, IOException {
        long time = SystemClock.elapsedRealtime();
        MediaExtractor extractor = new MediaExtractor();
        MediaFormat format = null;
        MediaCodec codec = null;
        try {
            long startTime = System.currentTimeMillis();
            if (obj instanceof File) {
                File file = (File) obj;
                mFileSize = (int) file.length();
                extractor.setDataSource(file.getPath());
            } else if (obj instanceof Uri) {
                Uri uri = (Uri) obj;
                mFileSize = (int) FileUtils.getFileSize(uri);
                Pair<FileDescriptor, ParcelFileDescriptor> fileDescriptors = MediaDBUtils.getFileDescriptors(uri, "r");
                FileDescriptor fd = fileDescriptors.first;
                mPfd = fileDescriptors.second;
                if (fd == null) {
                    DebugUtil.i(TAG, "readFile error uri : " + uri + ", no fd find ");
                    return;
                }
                extractor.setDataSource(fd);
            } else {
                return;
            }
            DebugUtil.i(TAG, "setDataSource: deltaTime: " + (System.currentTimeMillis() - startTime));
            int numTracks = extractor.getTrackCount();
            int i = 0;

            // find and select the first audio track present in the file.
            for (i = 0; i < numTracks; i++) {
                format = extractor.getTrackFormat(i);

                if (format.getString(MediaFormat.KEY_MIME).startsWith("audio/")) {
                    extractor.selectTrack(i);
                    break;
                }
            }
            if (i == numTracks) {
                DebugUtil.e(TAG, "No audio track found in " + obj);

                if (mSoundFileListener != null) {
                    setDecodeFinish(false);
                    mSoundFileListener.decodedSoundFileFinish(false);
                }

                return;
            }
            if (format == null) {
                return;
            }
            mChannels = format.getInteger(MediaFormat.KEY_CHANNEL_COUNT);
            mSampleRate = format.getInteger(MediaFormat.KEY_SAMPLE_RATE);
            // Expected total number of samples per channel.
            mExpectedNumSamples =
                    (int) ((format.getLong(MediaFormat.KEY_DURATION) / 1000000.f) * mSampleRate + 0.5f);
            mDuration = (long) Math.ceil(format.getLong(MediaFormat.KEY_DURATION) / 1000F);
            mNeedWaveLineCount = (int) Math.ceil(mDuration / mOneWaveLineTime);

            DebugUtil.d(TAG, "mFileType = " + mFileType + " ,mFileSize = " + mFileSize + " ,numTracks = " + numTracks
                    + " ,mDuration = " + mDuration);

            codec = MediaCodec.createDecoderByType(format.getString(MediaFormat.KEY_MIME));
            codec.configure(format, null, null, 0);
            codec.start();

            ByteBuffer[] inputBuffers = codec.getInputBuffers();
            ByteBuffer[] outputBuffers = codec.getOutputBuffers();
            MediaCodec.BufferInfo info = new MediaCodec.BufferInfo();
            int decodedSamplesSize = 0;  // size of the output buffer containing decoded samples.
            byte[] decodedSamples = null;
            int sample_size = 0;
            long presentation_time;
            int tot_size_read = 0;
            boolean done_reading = false;
            // Set the size of the decoded samples buffer to 1MB (~6sec of a stereo stream at 44.1kHz).
            // For longer streams, the buffer size will be increased later on, calculating a rough
            // estimate of the total size needed to store all the samples in order to resize the buffer
            // only once.
            mDecodedBytes = ByteBuffer.allocate(1 << 20);
            Boolean firstSampleData = true;
            int count = 0;

            while (true) {
                if (mStopDecoded) {
                    if (mDecodedBytes.position() > 0) {
                        convertToWaveForm(mDecodedBytes);
                    }
                    break;
                }

                // read data from file and feed it to the decoder input buffers.
                int inputBufferIndex = codec.dequeueInputBuffer(10);
                if (!done_reading && inputBufferIndex >= 0) {
                    sample_size = extractor.readSampleData(inputBuffers[inputBufferIndex], 0);
                    if (firstSampleData
                            && format.getString(MediaFormat.KEY_MIME).equals("audio/mp4a-latm")
                            && sample_size == 2) {
                        // For some reasons on some devices (e.g. the Samsung S3) you should not
                        // provide the first two bytes of an AAC stream, otherwise the MediaCodec will
                        // crash. These two bytes do not contain music data but basic info on the
                        // stream (e.g. channel configuration and sampling frequency), and skipping them
                        // seems OK with other devices (MediaCodec has already been configured and
                        // already knows these parameters).
                        extractor.advance();
                        extractor.advance();
                        tot_size_read += sample_size;
                    } else if (sample_size < 0) {
                        // All samples have been read.
                        codec.queueInputBuffer(
                                inputBufferIndex, 0, 0, -1, MediaCodec.BUFFER_FLAG_END_OF_STREAM);
                        done_reading = true;
                    } else {
                        presentation_time = extractor.getSampleTime();
                        codec.queueInputBuffer(inputBufferIndex, 0, sample_size, presentation_time, 0);
                        extractor.advance();
                        extractor.advance();
                        tot_size_read += sample_size;
                    }

                    firstSampleData = false;
                }

                // Get decoded stream from the decoder output buffers.
                int outputBufferIndex = codec.dequeueOutputBuffer(info, 10);

                if (outputBufferIndex >= 0 && info.size > 0) {
                    if (decodedSamplesSize < info.size) {
                        decodedSamplesSize = info.size;
                        decodedSamples = new byte[decodedSamplesSize];
                    }

                    outputBuffers[outputBufferIndex].get(decodedSamples, 0, info.size);
                    outputBuffers[outputBufferIndex].clear();

                    // Check if buffer is big enough. Resize it if it's too small.
                    if (mDecodedBytes.remaining() < info.size) {
                        // Getting a rough estimate of the total size, allocate 20% more, and
                        // make sure to allocate at least 5MB more than the initial size.
                        int position = mDecodedBytes.position();
                        int newSize = (int) ((position * (1.0 * mFileSize / tot_size_read)) * 1.2);

                        if (newSize - position < info.size + 5 * (1 << 20)) {
                            newSize = position + info.size + 5 * (1 << 20);
                        }

                        ByteBuffer newDecodedBytes = null;
                        // Try to allocate memory. If we are OOM, try to run the garbage collector.
                        int retry = 10;

                        while (retry > 0) {
                            try {
                                newDecodedBytes = ByteBuffer.allocate(newSize);
                                break;
                            } catch (OutOfMemoryError oome) {
                                // setting android:largeHeap="true" in <application> seem to help not
                                // reaching this section.
                                retry--;
                            }
                        }

                        if (retry == 0) {
                            // Failed to allocate memory... Stop reading more data and finalize the
                            // instance with the data decoded so far.
                            break;
                        }

                        //ByteBuffer newDecodedBytes = ByteBuffer.allocate(newSize);
                        mDecodedBytes.rewind();
                        newDecodedBytes.put(mDecodedBytes);
                        mDecodedBytes = newDecodedBytes;
                        mDecodedBytes.position(position);
                    }

                    mDecodedBytes.put(decodedSamples, 0, info.size);
                    count++;

                    if ((count % 10 == 0) || (info.flags == MediaCodec.BUFFER_FLAG_END_OF_STREAM)) {
                        mDecodedSampleTotalSize += mDecodedBytes.position();
                        convertToWaveForm(mDecodedBytes);
                        mDecodedBytes.rewind();
                        //BUG 1904587 2019.3.20 begin
                        if (mDecodedReadyListener != null && !mReady && mAmplitudeList.size() >= 2 * mWaitAmplitudes) {
                            mReady = true;
                            mDecodedReadyListener.onReady();
                            DebugUtil.i(TAG, "wait amplitudes time :" + (SystemClock.elapsedRealtime() - time));
                        }
                        //BUG 1904587 2019.3.20 end
                    }
                    codec.releaseOutputBuffer(outputBufferIndex, false);
                } else if (outputBufferIndex == MediaCodec.INFO_OUTPUT_BUFFERS_CHANGED) {
                    outputBuffers = codec.getOutputBuffers();
                } else if (outputBufferIndex == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED) {
                    // Subsequent data will conform to new format.
                    // We could check that codec.getOutputFormat(), which is the new output format,
                    // is what we expect.
                }

                if ((info.flags & MediaCodec.BUFFER_FLAG_END_OF_STREAM) != 0
                        || (mDecodedSampleTotalSize / (2 * mChannels)) >= mExpectedNumSamples) {
                    // We got all the decoded data from the decoder. Stop here.
                    // Theoretically dequeueOutputBuffer(info, ...) should have set info.flags to
                    // MediaCodec.BUFFER_FLAG_END_OF_STREAM. However some phones (e.g. Samsung S3)
                    // won't do that for some files (e.g. with mono AAC files), in which case subsequent
                    // calls to dequeueOutputBuffer may result in the application crashing, without
                    // even an exception being thrown... Hence the second check.
                    // (for mono AAC files, the S3 will actually double each sample, as if the stream
                    // was stereo. The resulting stream is half what it's supposed to be and with a much
                    // lower pitch.)
                    if (mDecodedBytes.position() > 0) {
                        convertToWaveForm(mDecodedBytes);
                    }

                    break;
                }
            }

            if (mDecodedReadyListener != null && !mReady) {
                mReady = true;
                mDecodedReadyListener.onReady();
            }

            correctAmplitudeList();

            if (mSoundFileListener != null) {
                setDecodeFinish(!mStopDecoded);
                mSoundFileListener.decodedSoundFileFinish(!mStopDecoded);
            }
        } catch (Exception e) {
            DebugUtil.e(TAG, "readFile error: ", e);
        } finally {
            extractor.release();
            extractor = null;
            if (codec != null) {
                codec.stop();
                codec.release();
                codec = null;
            }
            if (mPfd != null) {
                mPfd.close();
                mPfd = null;
            }
        }

        if (mDecodedSamples != null) {
            mDecodedSamples.rewind();
        }

        DebugUtil.d(TAG, "decoder mp3 to pcm total time = " + (SystemClock.elapsedRealtime() - time) + " ,mNumSamples = "
                + mNumSamples + " ,mNumFrames = " + mNumFrames + " ,mChannels = " + mChannels + ", mAmplitudeList size = "
                + (mAmplitudeList != null ? mAmplitudeList.size() : 0));
    }

    private void convertToWaveForm(ByteBuffer buffer) {
        long time = SystemClock.elapsedRealtime();

        if (mNumFrames >= mNeedWaveLineCount) {
            return;
        }

        mNumSamples = buffer.position() / (mChannels * 2); // One sample = 2 bytes.
        int numFrames = mNumSamples / getSamplesPerFrame();

        if (mNumSamples % getSamplesPerFrame() != 0) {
            numFrames++;
        }

        mNumFrames += numFrames;
        buffer.rewind();
        buffer.order(ByteOrder.LITTLE_ENDIAN);
        mDecodedSamples = buffer.asShortBuffer();
        int gain = 0;
        int value = 0;

        for (int i = 0; i < numFrames; i++) {
            gain = -1;

            for (int j = 0; j < getSamplesPerFrame(); j++) {
                value = 0;

                for (int k = 0; k < mChannels; k++) {
                    if (mDecodedSamples.remaining() > 0) {
                        value += Math.abs(mDecodedSamples.get());
                    }
                }

                value /= mChannels;

                if (gain < value) {
                    gain = value;
                }
            }

            mAmplitudeList.add(gain);
        }

        mLastProgress = mProgress;
        mProgress = mAmplitudeList.size() * MAX_PROGRESS / mNeedWaveLineCount;
        if ((mProgressCallback != null) && (mLastProgress != mProgress) && (mProgress != MAX_PROGRESS)) {
            if (mProgress <= DEFAULT_PROGRESS) {
                mProgressCallback.getProgress(DEFAULT_PROGRESS);
            } else {
                mProgressCallback.getProgress(mProgress);
            }
        }
//        DebugUtil.d(TAG, "convertToWaveForm mNumSamples = " + mNumSamples + " ,numFrames = " + numFrames + " ,time = "
//                + (SystemClock.elapsedRealtime() - time));
    }

    private void correctAmplitudeList() {
        if ((mAmplitudeList == null) || mAmplitudeList.isEmpty()) {
            return;
        }

        if (mStopDecoded) {
            return;
        }

        int differ = 0;

        if (mAmplitudeList.size() > mNeedWaveLineCount) {
            differ = mAmplitudeList.size() - mNeedWaveLineCount;

            for (int i = 0; i < differ; i++) {
                mAmplitudeList.remove(0);
            }
        } else if ((mAmplitudeList.size() < mNeedWaveLineCount) && (mAmplitudeList.size() > 0)) {
            Integer ampValue = mAmplitudeList.get(mAmplitudeList.size() - 1);
            int lastAmplitude = (ampValue == null) ? 0 : ampValue;
            int addAmplitude = 0;
            differ = mNeedWaveLineCount - mAmplitudeList.size();

            for (int i = 0; i < differ; i++) {
                addAmplitude = (int) (lastAmplitude * Math.random());
                mAmplitudeList.add(addAmplitude);
            }
        }

        if (mProgressCallback != null) {
            mProgressCallback.getProgress(MAX_PROGRESS);
        }
    }

    public interface ProgressCallback {
        void getProgress(int progress);
    }

    public void setProgressCallback(ProgressCallback callback) {
        mProgressCallback = callback;
    }
}
