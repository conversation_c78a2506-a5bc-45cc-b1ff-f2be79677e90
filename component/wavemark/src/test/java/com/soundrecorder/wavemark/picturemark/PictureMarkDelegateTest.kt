/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: PictureMarkDelegateTest
 * Description:
 * Version: 1.0
 * Date: 2022/10/27
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2022/10/27 1.0 create
 */

package com.soundrecorder.wavemark.picturemark

import android.content.Context
import android.os.Build
import android.os.Bundle
import androidx.activity.result.ActivityResultRegistry
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.LifecycleOwner
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.modulerouter.waveMark.IIPictureMarkListener
import com.soundrecorder.modulerouter.waveMark.IPictureMarkLifeOwnerProvider
import com.soundrecorder.wavemark.mark.MarkHelper
import com.soundrecorder.wavemark.shadows.ShadowFeatureOption
import com.soundrecorder.wavemark.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mockito
import org.mockito.Mockito.spy
import org.powermock.reflect.Whitebox
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(sdk = [Build.VERSION_CODES.S], shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class])
class PictureMarkDelegateTest {

    private var mContext: Context? = null
    private var mActivity: AppCompatActivity? = null
    private var mPictureMarkDelegate: PictureMarkDelegate? = null
    private var mViewModel: PictureMarkBaseActivityViewModel? = null

    private val iOwnerProvider = object : IPictureMarkLifeOwnerProvider {
        override fun provideLifeCycleOwner(): LifecycleOwner = mActivity!!

        override fun provideActivity(): AppCompatActivity = mActivity!!

        override fun isFinishing(): Boolean = (mActivity?.isFinishing == true)
    }

    @Before
    fun setUp() {
        mContext = ApplicationProvider.getApplicationContext()
        mActivity = spy(AppCompatActivity::class.java)
        //mViewModel = ViewModelProvider(mActivity!!)[PictureMarkBaseActivityViewModel::class.java]
        val registry: ActivityResultRegistry = Mockito.mock(ActivityResultRegistry::class.java)
        Mockito.doReturn(mContext).`when`(mActivity)?.applicationContext
        Mockito.doReturn(registry).`when`(mActivity)?.activityResultRegistry
    }

    @After
    fun tearDown() {
        mContext = null
        mActivity = null
    }

    @Test
    fun should_correct_when_checkNeedAddMark() {
        val listener = Mockito.mock(IIPictureMarkListener::class.java)
        mPictureMarkDelegate = PictureMarkDelegate(iOwnerProvider, false, listener as? IIPictureMarkListener<MarkMetaData, MarkDataBean>)
        val list = mutableListOf<MarkDataBean>(
            MarkDataBean(1000L),
            MarkDataBean(2000L),
            MarkDataBean(3000L)
        )
        val markDataList = spy(list)
        Mockito.doReturn(MarkHelper.MAX_MARKER_COUNT, 3).`when`(markDataList).size
        Mockito.doReturn(markDataList).`when`(listener).getMarkList()
        mPictureMarkDelegate?.checkNeedAddMark()?.let { Assert.assertFalse(it) }
        /*Assert.assertNotNull(ShadowToast.getLatestToast())
        Assert.assertEquals(mContext?.resources?.getString(R.string.namelength_outofrange), ShadowToast.getTextOfLatestToast())*/
        Mockito.doReturn(3000L, 5000L).`when`(listener).getPlayerCurrentTimeMillis()
        Mockito.doReturn(5000L, 7000L).`when`(listener).getDuration()
        mPictureMarkDelegate?.checkNeedAddMark()?.let { Assert.assertFalse(it) }
        mPictureMarkDelegate?.checkNeedAddMark()?.let { Assert.assertFalse(it) }
        mPictureMarkDelegate?.checkNeedAddMark()?.let { Assert.assertTrue(it) }
    }

    @Test
    fun should_launch_when_launchTackPhotoAlbum() {
        mPictureMarkDelegate = PictureMarkDelegate(iOwnerProvider, false, null)
        val result = Whitebox.invokeMethod<Boolean>(mPictureMarkDelegate, "launchTackPhotoAlbum", 1000L)
        //相册需要真机环境，所以这里只能断言为 false
        Assert.assertFalse(result)
    }

    @Test
    fun should_launch_when_launchTakePicture() {
        mPictureMarkDelegate = PictureMarkDelegate(iOwnerProvider, false, null)
        val result = Whitebox.invokeMethod<Boolean>(mPictureMarkDelegate, "launchTakePicture", 1000L)
        //相机需要真机环境，所以这里只能断言为 false
        Assert.assertFalse(result)
    }

    @Test
    fun should_check_when_checkNeedAddMark() {
        val listener = Mockito.mock(IIPictureMarkListener::class.java)
        Mockito.doReturn(1000L).`when`(listener).getDuration()

        mPictureMarkDelegate = PictureMarkDelegate(iOwnerProvider, false, listener as? IIPictureMarkListener<MarkMetaData, MarkDataBean>).apply {
            Assert.assertTrue(this.checkNeedAddMark())
        }
    }

    @Test
    fun should_release_when_releaseDialog() {
        mPictureMarkDelegate = PictureMarkDelegate(iOwnerProvider, false, null)
        Whitebox.invokeMethod<Boolean>(mPictureMarkDelegate, "releaseDialog")
        val internalState = Whitebox.getInternalState<AlertDialog>(mPictureMarkDelegate, "mSelectPictureDialog")
        Assert.assertNull(internalState)
    }

    @Test
    fun should_release_when_onCreate() {
        val listener = Mockito.mock(IIPictureMarkListener::class.java)
        mPictureMarkDelegate = PictureMarkDelegate(iOwnerProvider, false, listener as? IIPictureMarkListener<MarkMetaData, MarkDataBean>)
        val lifecycleOwner: LifecycleOwner = Mockito.mock(LifecycleOwner::class.java)
        mPictureMarkDelegate?.onCreate(lifecycleOwner)
    }

    @Test
    fun should_release_when_onSaveInstanceState() {
        mPictureMarkDelegate = PictureMarkDelegate(iOwnerProvider, false, null)
        val bundle = Mockito.mock(Bundle::class.java)
        mPictureMarkDelegate?.onSaveInstanceState(bundle)
    }

    @Test
    fun should_release_when_onRestoreInstanceState() {
        mPictureMarkDelegate = PictureMarkDelegate(iOwnerProvider, false, null)
        val bundle = Mockito.mock(Bundle::class.java)
        mPictureMarkDelegate?.onRestoreInstanceState(bundle)
    }
}