package com.soundrecorder.wavemark.picturemark;

import android.content.Context;
import android.content.Intent;
import android.os.Build;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.soundrecorder.wavemark.shadows.ShadowFeatureOption;
import com.soundrecorder.wavemark.shadows.ShadowOS12FeatureUtil;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.robolectric.annotation.Config;
import org.robolectric.shadows.ShadowLog;

import kotlin.Unit;

@RunWith(AndroidJUnit4.class)
@Config(sdk = Build.VERSION_CODES.S, shadows = {ShadowLog.class, ShadowOS12FeatureUtil.class, ShadowFeatureOption.class})
public class TakePhotoAlbumTest {

    private Context mContext;

    @Before
    public void setUp() {
        mContext = ApplicationProvider.getApplicationContext();
    }

    @After
    public void tearDown() {
        mContext = null;
    }

    @Test
    @PrepareForTest(TakePhotoAlbum.class)
    public void createIntent() {
        TakePhotoAlbum takePhotoAlbum = Mockito.spy(new TakePhotoAlbum());
        Intent intent = takePhotoAlbum.createIntent(mContext, Unit.INSTANCE);
        Assert.assertNotNull(intent);
    }

}
