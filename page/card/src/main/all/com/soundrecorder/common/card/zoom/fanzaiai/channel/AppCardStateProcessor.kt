/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  AppCardStateProcessor.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/3/31
 * * Author      : W9084994
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.soundrecorder.common.card.zoom.fanzaiai.channel

import android.content.Context
import android.widget.Toast
import com.google.gson.GsonBuilder
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.PrefUtil
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.buryingpoint.SummaryStaticUtil
import com.soundrecorder.common.card.R
import com.soundrecorder.common.card.zoom.fanzaiai.channel.AppCardStateProcessor.StorageSdkData.EXPIRED_TIME
import com.soundrecorder.common.card.zoom.fanzaiai.channel.AppCardStateProcessor.StorageSdkData.KEY_AUDIO_FILE_INFO_DATA
import com.soundrecorder.common.card.zoom.fanzaiai.channel.AppCardStateProcessor.StorageSdkData.REQUEST_RESULT_SUCCESS
import com.soundrecorder.common.card.zoom.fanzaiai.channel.AppCardStateProcessor.StorageSdkData.SOURCE_RPK_PACKAGE
import com.soundrecorder.common.card.zoom.fanzaiai.channel.SummaryValidator.SummaryStatusCode
import com.soundrecorder.common.card.zoom.fanzaiai.channel.SummaryValidator.UIStatusCode
import com.soundrecorder.common.card.zoom.fanzaiai.channel.SummaryValidator.isNeedStartAISummary
import com.soundrecorder.common.card.zoom.fanzaiai.channel.SummaryValidator.isNoteExist
import com.soundrecorder.common.card.zoom.fanzaiai.channel.processor.AudioProgressSimulator
import com.soundrecorder.common.card.zoom.fanzaiai.channel.processor.StartAISummaryProcessor
import com.soundrecorder.common.card.zoom.fanzaiai.fanzai.MetisRecordingStatusSender
import com.soundrecorder.common.databean.ConvertStatus
import com.soundrecorder.common.databean.Record
import com.soundrecorder.common.databean.Sentence
import com.soundrecorder.common.db.MediaDBUtils
import com.soundrecorder.common.db.NoteDbUtils
import com.soundrecorder.common.utils.ConvertDbUtil
import com.soundrecorder.common.utils.RecordModeUtil.getRecordTypeForMediaRecord
import com.soundrecorder.modulerouter.convertService.ConvertCheckUtilsAction
import com.soundrecorder.modulerouter.convertService.ConvertTaskAction
import com.soundrecorder.modulerouter.convertService.ConvertTaskStatus
import com.soundrecorder.modulerouter.convertService.ConvertThreadManageAction
import com.soundrecorder.modulerouter.smartname.SmartNameAction
import com.soundrecorder.modulerouter.summary.ERROR_CODE_DURATION_LARGE_WARN
import com.soundrecorder.modulerouter.summary.ERROR_CODE_SUCCESS
import com.soundrecorder.modulerouter.summary.SummaryInterface
import com.soundrecorder.modulerouter.utils.Injector
import org.hapjs.storage.InstantStorage
import java.io.File
import java.util.concurrent.CopyOnWriteArraySet
import kotlin.math.abs

object AppCardStateProcessor {

    private const val TAG = "AppCardStateProcessor"

    /**
     * 当卡片显示
     */
    const val ON_CARD_SHOW = 1004

    /**
     * 当卡片隐藏
     */
    const val ON_CARD_HIDE = 1005

    /**
     * 当卡片销毁
     */
    const val ON_CARD_DESTROY = 1006

    private var lastExecutionTime: Long = 0

    private const val DEBOUNCE_INTERVAL: Long = 300

    /**
     * 最新的mediaId
     */
    private const val KEY_LATEST_MEDIA_ID = "latestMediaId"

    /**
     * 正在处理中的转摘要
     */
    private val inProcessSummarySet = CopyOnWriteArraySet<Long>()

    /**
     * 正在处理中的转文本
     */
    private val inProcessConvertSet = CopyOnWriteArraySet<Long>()

    /**
     * 模拟转文本进度每次更新的进度
     */
    private const val MACK_PROGRESS_STEP = 15

    /**
     * 模拟转文本进度的最大值
     */
    private const val MAX_MOCK_PROGRESS = 95

    private var convertTextProgress = 0

    object StorageSdkData {
        //接入storage SDK存数据的KEY
        const val KEY_AUDIO_FILE_INFO_DATA = "audio_file_info_data"

        //请求方快应用rpk包名
        const val SOURCE_RPK_PACKAGE = "com.coloros.soundrecorder.card"

        const val EXPIRED_TIME = 10000000L
        const val REQUEST_RESULT_SUCCESS = 200
    }

    @Volatile
    var cardState = ON_CARD_DESTROY


    private val convertCheckUtilAction by lazy {
        Injector.injectFactory<ConvertCheckUtilsAction>()
    }

    private val convertThreadManageAction by lazy {
        Injector.injectFactory<ConvertThreadManageAction>()
    }

    private val summaryApi by lazy {
        Injector.injectFactory<SummaryInterface>()
    }

    private val convertTaskAction by lazy {
        Injector.injectFactory<ConvertTaskAction>()
    }

    private val smartNameApi by lazy {
        Injector.injectFactory<SmartNameAction>()
    }

    @JvmStatic
    fun updateCard(
        context: Context,
        forceUpdate: Boolean = false,
        audioFileInfo: AudioFileInfo
    ) {
        AudioProgressSimulator.stopSendProgress()
        val sendSuccess = AppCardMessageSender.sendMessage(audioFileInfo)
        if (!sendSuccess && forceUpdate) { // 强制更新直接通过metis推送刷新卡片
            MetisRecordingStatusSender.sendUpdateRecordEvent(context, audioFileInfo.mediaId)
        }

        // 通过channelSDK更新失败的时候，如果卡片没有销毁，需要通过storage SDK来刷新卡片。
        DebugUtil.d(TAG, "updateCard cardState:$cardState")
        if (!sendSuccess && cardState != ON_CARD_DESTROY) {
            writeStorageData(context, audioFileInfo2Json(audioFileInfo))
        }
        AppCardMessageSender.cancelDelayTask()
    }

    private fun audioFileInfo2Json(audioFileInfo: AudioFileInfo): String {
        val gson = GsonBuilder().disableHtmlEscaping().create()
        return gson.toJson(audioFileInfo)
    }

    fun checkIsGenSummary(mediaId: Long): Boolean {
        return inProcessSummarySet.contains(mediaId)
    }

    fun checkIsConvertText(mediaId: Long): Boolean {
        return inProcessConvertSet.contains(mediaId)
    }

    fun onRequestPermissionCallBack(hasPermission: Boolean, currentCode: Int?) {
        DebugUtil.i(TAG, "onRequestPermissionCallBack currentCode=$currentCode hasPermission=$hasPermission")
        if (hasPermission) {
            val context = BaseApplication.getAppContext()
            AppCardAudioFileObserver.updateAudioFileInfo()
            val audioFileInfo = AppCardAudioFileObserver.getAudioFileInfo()
            when (currentCode) {
                AppCardEventProcessor.ON_CLICK_START_SUMMARY -> tryGenSummary(context, audioFileInfo)

                AppCardEventProcessor.ON_CLICK_START_TEXT -> tryStartConvertText(context, audioFileInfo)

                AppCardEventProcessor.CHECK_JUMP_SUMMARY_DETAIL -> tryJumpSummaryDetail(context, audioFileInfo)

                AppCardEventProcessor.CHECK_JUMP_TEXT_DETAIL -> {
                    val jumpUri = AppCardAudioFileObserver.genTextJumpUri(audioFileInfo)
                    AppCardMessageSender.sendCheckResult(true, jumpUri)
                }
            }
        }
    }

    /**
     * Jump to the summary details page of the note
     */
    fun tryJumpSummaryDetailNotes(context: Context, audioFileInfo: AudioFileInfo) {
        val noteData = NoteDbUtils.queryNoteByMediaId(audioFileInfo.mediaId.toString())
        val bNoteExist = isNoteExist(noteData)
        DebugUtil.d(
            TAG,
            "tryJumpSummaryDetailNotes isNoteExist=$bNoteExist mediaId=${audioFileInfo.mediaId}"
        )
        if (!bNoteExist) {
            // 摘要已删除的情况，弹出toast提示，页面切换为转摘要
            noteData?.noteId?.let {
                NoteDbUtils.deleteNoteByNoteId(it)
            }
            val tip = context.getString(R.string.tip_summary_be_deleted)
            ToastManager.showToast(context, tip, Toast.LENGTH_SHORT)
            audioFileInfo.status = STATUS_CONVERT_SUMMARY
            AppCardMessageSender.sendMessage(audioFileInfo)
        } else {
            val jumpUri = AppCardAudioFileObserver.genNoteJumpUri(noteData?.noteId, noteData?.recordUUID)
            AppCardMessageSender.sendCheckResult(true, jumpUri)
        }
    }

    /**
     * Jump to the AI summary details
     */
    fun tryJumpAISummaryDetail(context: Context, audioFileInfo: AudioFileInfo) {
        DebugUtil.d(TAG, "tryJumpAISummaryDetail mediaId=${audioFileInfo.mediaId} ,${summaryApi?.getAISummaryIsExists(audioFileInfo.mediaId)}")
        if (summaryApi?.getAISummaryIsExists(audioFileInfo.mediaId) == true) {
            val jumpUri = AppCardAudioFileObserver.getAISummaryJumpUri(audioFileInfo)
            AppCardMessageSender.sendCheckResult(true, jumpUri, true)
        } else {
            // 摘要已删除的情况，弹出toast提示，页面切换为转摘要
            val tip = context.getString(R.string.tip_summary_be_deleted)
            ToastManager.showToast(context, tip, Toast.LENGTH_SHORT)
            audioFileInfo.status = STATUS_CONVERT_SUMMARY
            AppCardMessageSender.sendMessage(audioFileInfo)
        }
    }

    fun tryGenSummary(context: Context, audioFileInfo: AudioFileInfo) {
        val summaryPreCondition = SummaryValidator.checkSummaryPreCondition(context, audioFileInfo)

        when (summaryPreCondition.first) {
            ERROR_CODE_SUCCESS -> toGenRecordSummary(audioFileInfo)

            ERROR_CODE_DURATION_LARGE_WARN -> { // 时长大于2小时。
                ToastManager.showShortToast(
                    context,
                    com.soundrecorder.common.R.string.summary_warn_record_long
                )
                toGenRecordSummary(audioFileInfo)
            }

            else -> {
                DebugUtil.d(TAG, "tryGenSummary onMessage ON_CLICK_START_SUMMARY 3")
                DataQueryUtil.processSummaryOrConvertStatus(audioFileInfo)
                updateCardErrorMessage(context, audioFileInfo, STATUS_SUMMARY_ERROR_INFO, summaryPreCondition.second)
            }
        }
    }

    fun tryJumpSummaryDetail(context: Context, audioFileInfo: AudioFileInfo) {
        var localAudioFileInfo = audioFileInfo
        if (localAudioFileInfo.mediaId < 0) {
            localAudioFileInfo = AppCardAudioFileObserver.getAudioFileInfo()
            DebugUtil.w(TAG, "tryJumpSummaryDetail audioFileInfo.mediaId is illegal! localAudioFileInfo:$localAudioFileInfo")
        }
        if (summaryApi?.isDeviceSupportAIUnit(context) == true) {
            tryJumpAISummaryDetail(context, localAudioFileInfo)
        } else {
            tryJumpSummaryDetailNotes(context, localAudioFileInfo)
        }
    }

    fun tryStartConvertText(context: Context, audioFileInfo: AudioFileInfo) {
        DebugUtil.d(
            TAG,
            "tryStartConvertText ON_CLICK_START_TEXT audioFileInfo.size:${audioFileInfo.size}, mDuration:${audioFileInfo.mDuration}"
        )
        if (false == convertCheckUtilAction?.isFileSizeMaxMet(audioFileInfo.size)) { //大于500M
            ToastManager.showShortToast(
                context,
                com.soundrecorder.common.R.string.convert_error_size_long
            )
        } else if (false == convertCheckUtilAction?.isFileDurationMaxMet(audioFileInfo.mDuration)) { //大于5小时
            ToastManager.showShortToast(
                context,
                com.soundrecorder.common.R.string.convert_error_duration_long
            )
        } else {
            when (convertThreadManageAction?.checkCanAddNewTask(audioFileInfo.mediaId)) {
                // 转文本任务数已达上限
                ConvertTaskStatus.OVER_LIMIT -> {
                    ToastManager.showShortToast(
                        context,
                        com.soundrecorder.common.R.string.convert_recordings_exceeds_upper_limit_v2
                    )
                }

                ConvertTaskStatus.ALREADY_RUNNING -> {
                    DebugUtil.i(
                        TAG,
                        "tryStartConvertText checkCanAddNewTask: ${audioFileInfo.mediaId}, already running, no need to "
                    )
                }

                else -> startConvertText(audioFileInfo)
            }
        }
    }

    private fun toGenRecordSummary(audioFileInfo: AudioFileInfo) {
        if (audioFileInfo.data.isEmpty()) {
            DebugUtil.d(TAG, "toGenRecordSummary onMessage toGenRecordSummary isEmpty")
            startGenSummary(AppCardAudioFileObserver.getAudioFileInfo())
        } else {
            DebugUtil.d(TAG, "toGenRecordSummary onMessage toGenRecordSummary ")
            startGenSummary(audioFileInfo)
        }
    }

    /**
     * Non-AISummary starts generating summaries
     */
    fun startGenSummaryInNotes(appCardEvent: AudioFileInfo) {
        val record = MediaDBUtils.queryRecordByFullPath(appCardEvent.data)
        if (record == null) {
            DebugUtil.w(TAG, "startGenSummaryInNotes record is null")
        } else {
            record.let {
                summaryApi?.startSummaryNoPreCheck(
                    SummaryStaticUtil.LAUNCHER_RECORD,
                    it,
                    listOf<Sentence>(),
                    it.markDataBeanList
                )
            }
        }
    }

    private fun startGenSummary(audioFileInfo: AudioFileInfo) {
        val context = BaseApplication.getAppContext()
        // Determine whether you need to convert text
        val convertRecord = ConvertDbUtil.selectByRecordId(audioFileInfo.mediaId)
        val isConvertComplete = ConvertDbUtil.checkAlreadyConvertComplete(convertRecord)
        val convertFileName = summaryApi?.getConvertFileNameByMediaId(audioFileInfo.mediaId.toLong())

        var file: File? = null
        convertFileName?.let {
            file = File(summaryApi?.getConvertSavePath(context), convertFileName)
        }

        val copyFile = file
        DebugUtil.d(TAG, "startGenSummary isConvertComplete:$isConvertComplete,copyFile.exists():${copyFile?.exists()}")
        if (summaryApi?.isDeviceSupportAIUnit(context) == true) {
            // To check if the plugin has been downloaded
            isNeedStartAISummary { result ->
                DebugUtil.d(TAG, "startGenSummary isNeedStartAISummary result:$result")
                if (result) {
                    if (isConvertComplete && copyFile != null && copyFile.exists()) {
                        // convert text data is ok, just start AI Summary.
                        processAISummary(audioFileInfo.mediaId, false)
                    } else {
                        tryStartConvertText(context, audioFileInfo)
                    }
                }
            }
        } else {
            startGenSummaryInNotes(audioFileInfo)
        }
    }

    /**
     * 新建录音时候刷新数据
     */
    @Synchronized
    fun onRecordAdd() {
        val currentTime = System.currentTimeMillis()
        val context = BaseApplication.getAppContext()
        if (abs(currentTime - lastExecutionTime) > DEBOUNCE_INTERVAL) {
            lastExecutionTime = currentTime
            AppCardMessageSender.executeRunnable {
                runCatching {
                    if (AppCardAudioFileObserver.updateAudioFileInfo()) {
                        PrefUtil.putLong(
                            BaseApplication.getAppContext(),
                            KEY_LATEST_MEDIA_ID,
                            AppCardAudioFileObserver.getAudioFileInfo().mediaId
                        )
                        updateCard(context, true, AppCardAudioFileObserver.getAudioFileInfo())
                    }
                }.onFailure {
                    DebugUtil.e(TAG, "recycleRecordData error=${it.message} ")
                }
            }
        } else {
            DebugUtil.d("DebounceUtil", "updateRecordData Action is debounced.")
        }
    }

    @Synchronized
    fun onRecordDelete() {
        AppCardMessageSender.executeRunnable {
            val currentAudioExist = MediaDBUtils.queryAudioFileExist(AppCardAudioFileObserver.getAudioFileInfo().mediaId)
            if (currentAudioExist) { // 当前卡片显示音频文件没有被删除，则忽略本次删除操作
                DebugUtil.i(TAG, "onRecordDelete return current not delete")
                return@executeRunnable
            }

            val context = BaseApplication.getAppContext()
            val newestAudio = DataQueryUtil.getNewestAudioInfo()
            if (newestAudio == null) {
                AudioProgressSimulator.stopSendProgress()
                PrefUtil.putLong(context, KEY_LATEST_MEDIA_ID, -1L)
                AppCardAudioFileObserver.getAudioFileInfo().reset()
                MetisRecordingStatusSender.sendDeleteAllRecordEvent(context)
                DebugUtil.i(TAG, "onRecordDelete delete all")
            } else {
                AppCardAudioFileObserver.updateAudioFileInfo(newestAudio)
                PrefUtil.putLong(context, KEY_LATEST_MEDIA_ID, newestAudio.mediaId)
                updateCard(context, audioFileInfo = AppCardAudioFileObserver.getAudioFileInfo())
                DebugUtil.i(TAG, "onRecordDelete currentMediaId=${newestAudio.mediaId} cardState=$cardState")
            }
        }
    }

    fun onRecordRename(mediaId: Long) {
        val audioFileInfo = AppCardAudioFileObserver.getAudioFileInfo()
        if (audioFileInfo.mediaId >= 0 && audioFileInfo.mediaId != mediaId) {
            DebugUtil.i(TAG, "onRecordRename media id not match")
            return
        }
        AppCardMessageSender.executeRunnable {
            DebugUtil.i(TAG, "onRecordRename")
            AppCardAudioFileObserver.updateAudioFileInfo()
            updateCard(
                BaseApplication.getAppContext(),
                audioFileInfo = AppCardAudioFileObserver.getAudioFileInfo()
            )
        }
    }

    fun onRecordRecover() {
        AppCardMessageSender.executeRunnable {
            val newestAudio = DataQueryUtil.getNewestAudioInfo()
            if (newestAudio == null) {
                DebugUtil.w(TAG, "onRecordRecover return by newest audio null")
                return@executeRunnable
            }
            val currentAudio = AppCardAudioFileObserver.getAudioFileInfo()
            /*媒体库查询排序是通过DATE_MODIFY + TITLE 排序的*/
            if ((newestAudio.mediaId != currentAudio.mediaId) && (newestAudio.dateModified >= currentAudio.dateModified)) {
                val context = BaseApplication.getAppContext()
                AppCardAudioFileObserver.updateAudioFileInfo(newestAudio)
                updateCard(context, audioFileInfo = AppCardAudioFileObserver.getAudioFileInfo())
                PrefUtil.putLong(context, KEY_LATEST_MEDIA_ID, newestAudio.mediaId)
                DebugUtil.i(TAG, "onRecordRecover update")
            }
        }
    }

    /**
     * 转文本生成后，检查更新卡片状态。
     * @param mediaId 需要检查更新的mediaId
     */
    fun onUpdateConvertStatus(mediaId: Long) {
        AppCardMessageSender.executeRunnable {
            val currentAudio = AppCardAudioFileObserver.getAudioFileInfo()
            DebugUtil.d(TAG, "onUpdateConvertStatus mediaId:$mediaId, currentAudio:$currentAudio")
            if (mediaId == currentAudio.mediaId) {
                val localAudioFileInfo = DataQueryUtil.processSummaryOrConvertStatus(currentAudio)
                updateCard(BaseApplication.getAppContext(), audioFileInfo = localAudioFileInfo)
                PrefUtil.putLong(
                    BaseApplication.getAppContext(),
                    KEY_LATEST_MEDIA_ID,
                    localAudioFileInfo.mediaId
                )
            }
        }
    }

    fun onConvertProgressChanged(
        mediaId: Long,
        uploadProgress: Int,
        convertProgress: Int,
        serverPlanCode: Int
    ) {
        val currentMediaId = AppCardAudioFileObserver.getAudioFileInfo().mediaId
        DebugUtil.d(
            TAG,
            "onConvertProgressChanged mediaId=$mediaId currentMediaId=$currentMediaId convertProgress=$convertProgress " +
            ",summaryApi?.isSupportAISummary(context):" + "${summaryApi?.isDeviceSupportAIUnit(BaseApplication.getAppContext())}"
        )
        if (mediaId == currentMediaId && (summaryApi?.getSupportRecordSummaryValue()?.value == false || summaryApi?.isDeviceSupportAIUnit(
                BaseApplication.getAppContext()
            ) == true)
        ) {
            convertTextProgress += MACK_PROGRESS_STEP
            if (convertTextProgress > MAX_MOCK_PROGRESS) {
                convertTextProgress = MAX_MOCK_PROGRESS
            }
            AppCardMessageSender.sendProgress(
                STATUS_CONVERT_TEXT_IN_PROGRESS,
                convertTextProgress.toString()
            )
        }
    }

    fun onConvertStatusChange(
        mediaId: Long,
        uploadStatus: Int,
        convertStatus: Int,
        errorMessage: String
    ) {
        val currentMediaId = AppCardAudioFileObserver.getAudioFileInfo().mediaId
        DebugUtil.d(
            TAG,
            "onConvertStatusChange mediaId=$mediaId currentMediaId=$currentMediaId value=${summaryApi?.getSupportRecordSummaryValue()?.value}," +
                    "isSupportAISummary(context):${summaryApi?.isDeviceSupportAIUnit(BaseApplication.getAppContext())}"
        )
        if (errorMessage.isNotEmpty()) {
            inProcessConvertSet.add(mediaId)
        } else {
            inProcessConvertSet.remove(mediaId)
        }
        if (mediaId == currentMediaId && (summaryApi?.getSupportRecordSummaryValue()?.value == false || summaryApi?.isDeviceSupportAIUnit(
                BaseApplication.getAppContext()
            ) == true)) {
            AppCardMessageSender.executeRunnable {
                DebugUtil.d(
                    TAG,
                    "onConvertStatusChange uploadStatus=$uploadStatus convertStatus=$convertStatus errorMessage=$errorMessage"
                )
                if (convertStatus == ConvertStatus.CONVERT_STATUS_ABORTTASK_SUC
                    || (convertStatus == ConvertStatus.CONVERT_STATUS_UNINIT && uploadStatus == ConvertStatus.UPLOAD_STATUS_UPLOAD_ABORT_SUC)
                ) {
                    AppCardAudioFileObserver.getAudioFileInfo().run {
                        status = STATUS_CONVERT_TEXT
                        updateCard(BaseApplication.getAppContext(), audioFileInfo = this)
                    }
                } else if (errorMessage.isNotEmpty()) {
                    val context = BaseApplication.getAppContext()
                    val realErrorMsg = convertCheckUtilAction?.getErrorMsgByStatus(
                        context,
                        uploadStatus,
                        convertStatus,
                        errorMessage
                    ) ?: "convertServiceApi is null"
                    convertTextProgress = 0
                    val audioInfo = AppCardAudioFileObserver.getAudioFileInfo()
                    DataQueryUtil.processSummaryOrConvertStatus(audioInfo)
                    updateCardErrorMessage(
                        context,
                        audioInfo,
                        STATUS_CONVERT_ERROR_INFO,
                        realErrorMsg
                    )
                } else {
                    convertTextProgress += MACK_PROGRESS_STEP
                    if (convertTextProgress > MAX_MOCK_PROGRESS) {
                        convertTextProgress = MAX_MOCK_PROGRESS
                    }
                    AppCardMessageSender.sendProgress(
                        STATUS_CONVERT_TEXT_IN_PROGRESS,
                        convertTextProgress.toString()
                    )
                }
            }
        }
    }

    fun onConvertEnd(mediaId: Long) {
        inProcessConvertSet.remove(mediaId)
        val currentMediaId = AppCardAudioFileObserver.getAudioFileInfo().mediaId
        DebugUtil.d(
            TAG,
            "onConvertEnd mediaId=$mediaId currentMediaId=$currentMediaId value=${summaryApi?.getSupportRecordSummaryValue()?.value} ," +
                    "isSupportAISummary(context):${summaryApi?.isDeviceSupportAIUnit(BaseApplication.getAppContext())}"
        )
        if (mediaId == currentMediaId && (summaryApi?.getSupportRecordSummaryValue()?.value == false || summaryApi?.isDeviceSupportAIUnit(
                BaseApplication.getAppContext()
            ) == true)) {
            val audioFileInfo = AppCardAudioFileObserver.getAudioFileInfo()
            AppCardMessageSender.executeRunnable {
                audioFileInfo.status = STATUS_LOOK_TEXT
                audioFileInfo.uri = AppCardAudioFileObserver.genTextJumpUri(audioFileInfo)
                updateCard(BaseApplication.getAppContext(), audioFileInfo = audioFileInfo)
            }
            // normal convert text need to check if start process AI summary
            if (summaryApi?.getSupportRecordSummaryValue()?.value == true) {
                processAISummary(audioFileInfo.mediaId)
            }
        }
    }

    fun startSendSummaryProgress(startProgress: Int = 0, mediaId: Long?) {
        val currentMediaId = AppCardAudioFileObserver.getAudioFileInfo().mediaId
        DebugUtil.d(TAG, "onGenSummaryStart mediaId=$mediaId currentMediaId=$currentMediaId")
        if (mediaId == currentMediaId) {
            AppCardMessageSender.executeRunnable {
                AudioProgressSimulator.startSendProgress(startProgress, AppCardAudioFileObserver.getAudioFileInfo().mDuration) { pro ->
                    AppCardMessageSender.sendProgress(
                        STATUS_CONVERT_SUMMARY_IN_PROGRESS,
                        pro.toString()
                    )
                }
            }
        }
    }

    fun onGenSummaryStart(mediaId: Long?) {
        mediaId?.let {
            inProcessSummarySet.add(mediaId)
        }
        startSendSummaryProgress(mediaId = mediaId)
    }

    fun onSummaryProgressEnd(
        mediaId: Long?,
        noteId: String?,
        callUuid: String?,
        asrErrorCode: Int?,
        summaryErrorCode: Int?
    ) {
        mediaId?.let {
            inProcessSummarySet.remove(mediaId)
        }
        val currentMediaId = AppCardAudioFileObserver.getAudioFileInfo().mediaId
        DebugUtil.d(
            TAG,
            "onGenSummaryEnd mediaId=$mediaId currentMediaId=$currentMediaId " +
                    "noteId=$noteId callUuid=$callUuid"
        )
        if (mediaId == currentMediaId) {
            AppCardMessageSender.executeRunnable {
                val audioFileInfo = AppCardAudioFileObserver.getAudioFileInfo()
                DataQueryUtil.processSummaryOrConvertStatus(audioFileInfo)
                when (summaryErrorCode) {
                    SummaryStatusCode.SUMMARY_NORMAL_CODE -> {
                        if (asrErrorCode == SummaryStatusCode.SUMMARY_NORMAL_CODE || asrErrorCode == UIStatusCode.UI_ASR_ABORT_STREAM) {
                            updateCard(
                                BaseApplication.getAppContext(),
                                audioFileInfo = audioFileInfo
                            )
                        } else {
                            updateCardErrorMessage(BaseApplication.getAppContext(), audioFileInfo, STATUS_DEFAULT_SUMMARY_ERROR)
                        }
                    }

                    else -> updateCardErrorMessage(BaseApplication.getAppContext(), audioFileInfo, STATUS_DEFAULT_SUMMARY_ERROR)
                }
            }
        }
    }

    private fun startConvertText(appCardEvent: AudioFileInfo) {
        convertTextProgress = 0
        convertTaskAction?.startConvertTask(appCardEvent.mediaId)
        AppCardMessageSender.sendProgress(
            STATUS_CONVERT_TEXT_IN_PROGRESS, "0"
        )
    }

    private fun writeStorageData(context: Context, audioFileInfoJson: String) {
        val result = InstantStorage.getStorage(SOURCE_RPK_PACKAGE)
            ?.setSharedData(context, KEY_AUDIO_FILE_INFO_DATA, audioFileInfoJson, EXPIRED_TIME)
        DebugUtil.d(
            TAG,
            "writeStorageData InstantStorage.getStorage(SOURCE_RPK_PACKAGE):${InstantStorage.getStorage(SOURCE_RPK_PACKAGE)}, " +
                    "result:$result,audioFileInfoJson:$audioFileInfoJson"
        )
        if (result != REQUEST_RESULT_SUCCESS) {
            DebugUtil.e(TAG, "writeStorageData fail, error code: $result")
        }
    }

    private fun updateCardErrorMessage(context: Context, audioFileInfo: AudioFileInfo, errorType: Int, errorMessage: String = "") {
        AudioProgressSimulator.stopSendProgress()
        val sendSuccess = AppCardMessageSender.sendErrorMessage(errorType, errorMessage)
        // 通过channelSDK更新失败的时候，如果卡片没有销毁，需要通过发送意图来刷新卡片
        if (!sendSuccess && cardState != ON_CARD_DESTROY) {
            val sendData = audioFileInfo.copy().apply {
                this.status = errorType
                this.errorMessage = errorMessage
            }
            DebugUtil.i(TAG, "updateSendErrorMessage fail to force update")
            writeStorageData(context, audioFileInfo2Json(sendData))
            AppCardMessageSender.senDelayNormalMessage(audioFileInfo)
        }
    }

    internal fun processAISummary(mediaId: Long, isNeedCheck: Boolean = true) {
        if (isNeedCheck) {
            isNeedStartAISummary { result ->
                DebugUtil.d(TAG, "processAISummary start isNeedStartAISummary:$result.")
                if (result) {
                    // to start ai summary
                    startAISummary(mediaId)
                }
            }
        } else {
            startAISummary(mediaId)
        }
    }

    /**
     * Start the AISummary process
     */
    fun startAISummary(mediaId: Long) {
        AppCardAudioFileObserver.getAudioFileInfo().also { audioFileInfo ->
            if (mediaId == audioFileInfo.mediaId) {
                StartAISummaryProcessor.aiSummaryProcessor(
                    mediaId = audioFileInfo.mediaId,
                    recordType = getRecordTypeForMediaRecord(Record().apply {
                        data = audioFileInfo.data
                        relativePath = audioFileInfo.relativePath
                    }),
                    recordTime = audioFileInfo.dateAdd,
                    recordDuration = audioFileInfo.mDuration,
                    recordCallName = audioFileInfo.callName,
                    { onGenSummaryStart(audioFileInfo.mediaId) },
                    {
                        onSummaryProgressEnd(
                            audioFileInfo.mediaId,
                            null,
                            null,
                            null,
                            SummaryStatusCode.SUMMARY_EXCEPTION_CODE
                        )
                    },
                    {
                        onSummaryProgressEnd(
                            audioFileInfo.mediaId,
                            null,
                            null,
                            SummaryStatusCode.SUMMARY_NORMAL_CODE,
                            SummaryStatusCode.SUMMARY_NORMAL_CODE
                        )
                    }
                )
            } else {
                // The mediaId passed in does not match the one on the card
                DebugUtil.w(TAG, "startAISummary mediaId is mismatch !")
            }
        }
    }
}