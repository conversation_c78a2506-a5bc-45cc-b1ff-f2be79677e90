/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: PlaybackAnimatedCircleButton
 * Description:
 * Version: 1.0
 * Date: 2022/12/12
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2022/12/12 1.0 create
 */

package com.soundrecorder.playback.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import androidx.core.content.ContextCompat
import com.soundrecorder.base.utils.WindowType
import com.soundrecorder.common.widget.AnimatedCircleButton
import com.soundrecorder.playback.R
import androidx.core.content.withStyledAttributes

class PlaybackAnimatedCircleButton @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AnimatedCircleButton(context, attrs, defStyleAttr) {

    var windowType: WindowType = WindowType.SMALL
        set(value) {
            field = value
        }

    private var mDrawablePlaySubWindow: Drawable? = null
    private var mDrawablePauseSubWindow: Drawable? = null

    /*默认按钮背景色*/
    private var circleColorDefault: Int = DEFAULT_CIRCLE_COLOR
    /*中大屏下，按钮背景色*/
    private var circleColorOverSmallWindow: Int = DEFAULT_CIRCLE_COLOR

    init {
        context.withStyledAttributes(
            attrs,
            com.soundrecorder.common.R.styleable.AnimatedCircleButton,
            defStyleAttr,
            0
        ) {
            circleColorDefault = getColor(
                com.soundrecorder.common.R.styleable.AnimatedCircleButton_circle_color,
                DEFAULT_CIRCLE_COLOR
            )
            super.initMutationColor(this)
        }
        circleColorOverSmallWindow =
            ContextCompat.getColor(context, R.color.play_button_background_sub_window)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        mDrawablePlaySubWindow = null
        mDrawablePauseSubWindow = null
    }
}