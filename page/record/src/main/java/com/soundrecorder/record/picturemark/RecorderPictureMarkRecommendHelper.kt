/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RecorderPictureMarkRecommendHelper
 * Description:
 * Version: 1.0
 * Date: 2022/11/1
 * Author: ********(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * ******** 2022/11/1 1.0 create
 */

package com.soundrecorder.record.picturemark

import android.app.Activity
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.widget.ImageView
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.viewModelScope
import com.soundrecorder.base.BaseApplication
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.base.utils.FileUtils
import com.soundrecorder.base.utils.ToastManager
import com.soundrecorder.common.buryingpoint.BuryingPoint
import com.soundrecorder.common.databean.MarkMetaData
import com.soundrecorder.common.databean.markdata.MarkDataBean
import com.soundrecorder.imageload.utils.ImageUtils.uri2File
import com.soundrecorder.modulerouter.waveMark.IIPictureMarkListener
import com.soundrecorder.modulerouter.waveMark.IPictureMarkDelegate
import com.soundrecorder.modulerouter.waveMark.IPictureMarkLifeOwnerProvider
import com.soundrecorder.modulerouter.waveMark.WaveMarkInterface
import com.soundrecorder.modulerouter.photoviewer.PhotoViewerInterface
import com.soundrecorder.modulerouter.recorder.RecordInterface
import com.soundrecorder.modulerouter.recorder.RecorderServiceInterface
import com.soundrecorder.modulerouter.utils.Injector
import com.soundrecorder.wavemark.R
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class RecorderPictureMarkRecommendHelper(
    private val activity: AppCompatActivity,
    isActivityRecreate: Boolean,
    val listener: IIPictureMarkListener<MarkMetaData, MarkDataBean>?,
) : IPictureMarkLifeOwnerProvider {
    companion object {
        private const val SHOW_POP_VIEW_LOADING_REQUEST_CODE_X = 1002
        private const val SHOW_SINGLE_PICTURE_SAVE_REQUEST_CODE_X = 1003
    }

    private val mLogTag = "RecorderPictureMarkRecommendHelper"
    private var iPictureMarkDelegate: IPictureMarkDelegate<MarkMetaData>? = null
    private var pictureSelectLauncher: ActivityResultLauncher<Intent>? = null

    private val waveMarkApi by lazy {
        Injector.injectFactory<WaveMarkInterface>()
    }

    private val recordApi by lazy {
        Injector.injectFactory<RecordInterface>()
    }

    private val recorderViewModelApi by lazy {
        Injector.injectFactory<RecorderServiceInterface>()
    }

    private val photoViewerApi by lazy {
        Injector.injectFactory<PhotoViewerInterface>()
    }

    init {
        iPictureMarkDelegate = waveMarkApi?.newPictureMarkDelegate(this, isActivityRecreate, listener)
        initPictureSelect()
    }

    /**
     *需activity在对应生命周期onSaveInstanceState调用
     */
    fun onSaveInstanceState(outState: Bundle) {
        iPictureMarkDelegate?.onSaveInstanceState(outState)
    }

    /**
     *需activity在对应生命周期onRestoreInstanceState调用
     */
    fun onRestoreInstanceState(savedInstanceState: Bundle) {
        iPictureMarkDelegate?.onRestoreInstanceState(savedInstanceState)
    }

    /**
     *需activity在对应生命周期onConfigurationChanged调用
     */
    fun onConfigurationChanged(newConfig: Configuration) {
        iPictureMarkDelegate?.onConfigurationChanged(newConfig)
    }

    /**
     *需activity在对应生命周期onNewIntent调用
     */
    fun onNewIntent(intent: Intent?) {
        iPictureMarkDelegate?.onNewIntent(intent)
    }

    /**
     *需activity在对应生命周期startActivityForResult调用
     */
    fun setRequestCodeX(requestCode: Int) {
        iPictureMarkDelegate?.setRequestCodeX(requestCode)
    }

    fun getPictureMarkDelegate(): IPictureMarkDelegate<MarkMetaData>? = iPictureMarkDelegate

    fun isAddPictureMarking(): MutableLiveData<Boolean> = iPictureMarkDelegate?.isAddPictureMarking() ?: MutableLiveData(false)

    fun resetAddPictureMarking() {
        iPictureMarkDelegate?.setIsAddPictureMarking(false)
    }

    fun getRequestCodeX(): Int = iPictureMarkDelegate?.getRequestCodeX() ?: -1

    fun launchTakePhotoFromRecorderActivity(currentTimeMillis: Long): Boolean {
        val success = iPictureMarkDelegate?.launchTakePhotoSingle(IPictureMarkDelegate.SOURCE_CAMERA, currentTimeMillis) ?: false
        if (success) {
            iPictureMarkDelegate?.setIsAddPictureMarking(true)
        }
        return success
    }

    /**
     * 智能图片标记
     */
    private fun initPictureSelect() {
        if (iPictureMarkDelegate == null) {
            DebugUtil.e(mLogTag, "iPictureMarkDelegate is null")
            return
        }
        pictureSelectLauncher =
            activity.registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
                if (result.resultCode == Activity.RESULT_OK && result.data != null) {
                    val selectedPhotos =
                        result.data?.getParcelableArrayListExtra<PopPicture>("selectedPhotos")
                    if (!selectedPhotos.isNullOrEmpty()) {
                        if (selectedPhotos.size >= 2) {
                            activity.lifecycleScope.launchWhenResumed {
                                iPictureMarkDelegate?.setIsAddPictureMarking(true)
                            }
                            recorderViewModelApi?.setDoMultiPictureMarkLoading(true)
                            recordApi?.startPopViewLoadingActivity<MarkMetaData, PopPicture>(activity, selectedPhotos, {
                                val index = listener?.doMultiPictureMark(it) ?: -1
                                index
                            }, {
                                iPictureMarkDelegate?.setIsAddPictureMarking(false)
                                recorderViewModelApi?.setDoMultiPictureMarkLoading(false)
                            }, SHOW_POP_VIEW_LOADING_REQUEST_CODE_X)
                        } else {
                            iPictureMarkDelegate?.getViewModel()?.viewModelScope?.launch(Dispatchers.IO) {
                                iPictureMarkDelegate?.setIsAddPictureMarking(true)
                                val file = FileUtils.getAppFile()
                                val element = selectedPhotos.first()
                                val imageParceResult = element.data.uri2File(file)
                                if (imageParceResult != null) {
                                    val selectPhotoData = ArrayList<MarkMetaData>()
                                    val p = MarkMetaData(
                                        "",
                                        file.name,
                                        element.recordTime,
                                        imageParceResult.width,
                                        imageParceResult.height
                                    )
                                    selectPhotoData.add(p)
                                    withContext(Dispatchers.Main) {
                                        listener?.doMultiPictureMark(selectPhotoData)
                                    }
                                } else {
                                    ToastManager.showLongToast(
                                        BaseApplication.getAppContext(),
                                        R.string.error_picture_mark_content
                                    )
                                }
                                iPictureMarkDelegate?.setIsAddPictureMarking(false)
                            }
                        }
                        listener?.doSingleOrMultiPictureMarkEnd(false, IPictureMarkDelegate.SOURCE_MULTI_CAMERA)
                    } else {
                        listener?.doSingleOrMultiPictureMarkEnd(true, IPictureMarkDelegate.SOURCE_MULTI_CAMERA)
                    }
                } else {
                    listener?.doSingleOrMultiPictureMarkEnd(true, IPictureMarkDelegate.SOURCE_MULTI_CAMERA)
                }
                iPictureMarkDelegate?.setIsAddPictureMarking(false)
            }
    }

    /**
     * 智能图片标记-多张
     */
    fun launchPictureSelectFromRecorderActivity(intent: Intent): Boolean {
        try {
            iPictureMarkDelegate?.setIsAddPictureMarking(true)
            return pictureSelectLauncher?.let {
                it.launch(intent)
                true
            } ?: false
        } catch (ignored: Exception) {
            DebugUtil.e(mLogTag, "launch error", ignored)
        }
        return false
    }

    /**
     * 智能图片标记-一张
     */
    fun launchPhotoView(data: List<PopPicture>, transitionView: ImageView?) {
        iPictureMarkDelegate?.setIsAddPictureMarking(true)
        photoViewerApi?.startWithMultiPictureSelect(
            activity, data, transitionView,
            SHOW_SINGLE_PICTURE_SAVE_REQUEST_CODE_X
        ) { popPicture ->
            iPictureMarkDelegate?.setIsAddPictureMarking(false)
            if (popPicture != null) {
                iPictureMarkDelegate?.getViewModel()?.viewModelScope?.launch(Dispatchers.IO) {
                    val file = FileUtils.getAppFile()
                    val imageParceResult = popPicture.data.uri2File(file)
                    if (imageParceResult != null) {
                        withContext(Dispatchers.Main) {
                            val markMetaData = MarkMetaData(
                                "",
                                file.name,
                                popPicture.recordTime,
                                imageParceResult.width,
                                imageParceResult.height
                            )
                            listener?.doPictureMark(markMetaData)
                        }
                    } else {
                        ToastManager.showLongToast(activity, R.string.error_picture_mark_content)
                    }
                }
                BuryingPoint.oKAddPictureMarkNumber()
            } else {
                BuryingPoint.cancelAddPictureMarkNumber()
            }
        }
    }

    override fun provideLifeCycleOwner(): LifecycleOwner = activity

    override fun provideActivity(): AppCompatActivity = activity

    override fun isFinishing(): Boolean = activity.isFinishing
}