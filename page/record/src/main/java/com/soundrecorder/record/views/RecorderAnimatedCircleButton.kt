/**
 * Copyright (C), 2008-2022 OPLUS Mobile Comm Corp., Ltd.
 * File: RecorderAnimatedCircleButton
 * Description:
 * Version: 1.0
 * Date: 2023/7/27
 * Author: W9012818(<EMAIL>)
 * --------------------Revision History: ---------------------
 * <author> <date> <version> <desc>
 * W9012818 2023/7/27 1.0 create
 */

package com.soundrecorder.record.views

import android.content.Context
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import androidx.core.content.ContextCompat
import com.coui.appcompat.contextutil.COUIContextUtil
import com.soundrecorder.common.widget.AnimatedCircleButton
import com.soundrecorder.record.R

class RecorderAnimatedCircleButton @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : AnimatedCircleButton(context, attrs, defStyleAttr) {
    private var mNightDrawablePlay: Drawable? = null
    private var mLightDrawablePlay: Drawable? = null

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        mNightDrawablePlay = null
        mLightDrawablePlay = null
    }

    fun switchCallNightPlayState(isNightMode: Boolean) {
        if (isNightMode) {
            if (mNightDrawablePlay == null) {
                mNightDrawablePlay =
                    ContextCompat.getDrawable(context, R.drawable.ic_night_play_icon)
            }
            setImageDrawable(mNightDrawablePlay)
        } else {
            if (mLightDrawablePlay == null) {
                mLightDrawablePlay =
                    ContextCompat.getDrawable(context, R.drawable.ic_light_play_icon)
            }
            setImageDrawable(mLightDrawablePlay)
        }
    }

    override fun switchPlayState() {
        if (mDrawablePlay == null) {
            mDrawablePlay = ContextCompat.getDrawable(context, com.soundrecorder.common.R.drawable.ic_play)
        }
        contentDescription = context.getString(com.soundrecorder.common.R.string.talkback_pause)
        setImageDrawable(mDrawablePlay)
    }

    override fun switchPauseState() {
        if (mDrawablePause == null) {
            mDrawablePause = ContextCompat.getDrawable(context, com.soundrecorder.common.R.drawable.ic_pause)
        }
        contentDescription = context.getString(com.soundrecorder.common.R.string.talkback_play)
        setImageDrawable(mDrawablePause)
    }

    override fun setCircleColor(enable: Boolean) {
        mCircleColor = if (!enable) {
            COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPressBackground)
        } else {
            COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorCard)
        }
        invalidate()
    }
}