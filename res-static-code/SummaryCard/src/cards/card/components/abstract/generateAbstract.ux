<template>
  <div class="content">
    <div class="top-content">
      <div class="img-content">
        <image
          src="../../images/{{isDark ? 'dark-state' :'light-state'}}.svg"
          class="imgs"
        ></image>
      </div>
      <div style="height: 5%"></div>
      <div class="text-content">
        <text class="text" style="color:{{ isDark ? 'white' : 'black' }}">{{
          contentText.replace(' ', '')
        }}</text>
      </div>
    </div>
    <div class="btn-content" style="background-image: {{ bgImageUrl }}">
      <text class="btn-text {{ bgColor }}" onclick="generateAbstract">{{
        type === 'text'
          ? $t('message.generateAbstract.transfer_text')
          : $t('message.generateAbstract.generate_summary')
      }}</text>
    </div>
  </div>
</template>

<script>
import { formatTime } from '../../common/utils.js'
export default {
  props: {
    isDark: {
      type: Boolean,
      default: true,
    },
    type: {
      type: String,
      default: 'abstract',
    },
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      length: this.title.length,
      indexOf: this.title.indexOf('-'),
    }
  },
  computed: {
    imageUrl() {
      if (this.isDark) {
        return '/cards/card/images/dark-state.svg'
      }
      return '/cards/card/images/light-state.svg'
    },
    // 动态背景图
    bgImageUrl() {
      if (this.isDark) {
        return '/cards/card/images/generateAbstractbg2.png'
      } else {
        return '/cards/card/images/generateAbstractbg1.png'
      }
    },
    bgColor() {
      if (this.isDark) {
        return 'dark-bg'
      }
      return 'light-bg'
    },
    contentText() {
      return this.title
    },
  },
  generateAbstract() {
    this.$emit('changeStatus', 'doing')
  },
}
</script>
<style lang="less" scoped>
.content {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  padding: 16dp;
  .top-content {
    display: flex;
    flex-direction: column;
    height: 62%;
    justify-content: center;
  }
  .img-content {
    width: 100%;
    height: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    .imgs {
      height: 12px;
      width: 25px;
      object-fit: contain;
    }
  }
  .null {
    width: 100%;
    height: 6px;
  }

  .text-content {
    width: 100%;
    display: flex;
    flex: 1;
    justify-content: center;

    .text {
      font-family: OPPO Sans 4 SC;
      font-size: 16px;
      font-weight: 600;
      line-height: 20px;
      text-align: center;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
      lines: 2;
      text-overflow: ellipsis;
    }
  }
  .btn-content {
    width: 100%;
    height: 38%;
    display: flex;
    position: fixed;
    bottom: 0px;
    padding: 0 16dp;
    flex-direction: column;
    align-content: center;
    /* justify-content: center; */
    /* margin-top: 10px; */
    .btn-text {
      align-self: center;
      height: 32px;
      max-height: 100%;
      width: 100%;
      margin: auto auto 16dp;
      border-radius: 100px;
      lines: 1;
      text-overflow: ellipsis;
      font-family: OPPO Sans 4;
      font-size: 12px;
      font-weight: 500;
      line-height: 16px;
      text-align: center;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
    }
    .dark-bg {
      background-color: #353f3e;
      color: rgba(255, 255, 255, 0.9);
    }
    .light-bg {
      background-color: #ffffff;
      color: rgba(0, 0, 0, 0.9);
    }
  }
}
</style>
