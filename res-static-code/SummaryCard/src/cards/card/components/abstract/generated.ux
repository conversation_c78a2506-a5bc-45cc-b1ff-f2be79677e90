<template>
  <div class="content">
    <div class="top-content" id="top-content">
      <div class="img">
        <image
          @resize="changeWidth"
          style="width: {{ iconWidth }}; height: {{ iconWidth }}"
          src="../../images/success-state.png"
        ></image>
      </div>
      <div style="height: 5%"></div>
      <div class="text-content">
        <text class="text" style="{{ textStyle }}">{{
          type === 'text'
            ? $t('message.generated.transfer_view_text')
            : $t('message.generated.save_success')
        }}</text>
      </div>
      <div style="height: 4%"></div>
    </div>
    <div class="btn-content">
      <text class="btn-text {{ bgColor }}" onclick="showDetail">{{
        type === 'text'
          ? $t('message.generated.convert_end')
          : $t('message.generated.view_summary')
      }}</text>
    </div>
  </div>
</template>

<script>
import router from '@system.router'
export default {
  onInit() {
    // 简单格式化：
    this.$t('message.generated.view_summary')
  },
  data() {
    return {
      iconWidth: '0',
    }
  },
  props: {
    type: {
      type: String,
      default: 'abstract',
    },
    isDark: {
      type: Boolean,
      default: false,
    },
  },

  computed: {
    bgColor() {
      if (this.isDark) {
        return 'dark-bg'
      }
      return 'light-bg'
    },
    textStyle() {
      return {
        color: this.isDark ? '#ffffff' : '#191919',
      }
    },

    btnColor() {
      return {
        backgroundColor: this.isDark ? 'rgba(69, 69, 69, 0.8)' : '#ffffff',
        color: this.isDark ? '#ffffff' : '#191919',
      }
    },
  },
  showDetail() {
    this.$emit('changeStatus', 'done')
  },
  changeWidth() {
    const that = this
    this.$element('top-content').getBoundingClientRect({
      success: function (data) {
        const { top, bottom, left, right, width, height } = data
        const imgWidth = height * 0.45
        that.iconWidth = imgWidth + 'px'
      },
      fail: (errorData, errorCode) => {
        console.error('getBoundingClientRect fail', errorData, errorCode)
      },
      complete: function () {
        console.info('complete')
      },
    })
  },
}
</script>
<style lang="less">
.content {
  display: flex;
  flex-direction: column;
  align-content: center;
  justify-content: space-between;
  height: 100%;
  width: 100%;
  padding: 16dp;
  .top-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-content: center;
    flex: 1;
  }
  .img {
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    image {
      width: 0;
      height: auto;
    }
  }
  .text-content {
    align-self: center;

    .text {
      font-family: OPPO Sans 4 SC;
      font-size: 14px;
      text-align: center;
      font-weight: 600;
      lines: 2;
      text-overflow: ellipsis;
    }
  }
  .btn-content {
    width: 100%;
    height: 32px;
    display: flex;
    justify-content: center;
    .btn-text {
      align-self: center;
      height: 32px;
      width: 100%;
      border-radius: 100px;
      lines: 1;
      text-overflow: ellipsis;
      font-family: OPPO Sans 4;
      font-size: 12px;
      font-weight: 500;
      line-height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
    }
    .dark-bg {
      background-color: rgba(69, 69, 69, 0.8);
      color: #ffffff;
    }
    .light-bg {
      background-color: #ffffff;
      color: #000000;
    }
  }
}
</style>
