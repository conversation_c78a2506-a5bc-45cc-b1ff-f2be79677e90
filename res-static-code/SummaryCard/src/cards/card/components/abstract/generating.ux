<template>
  <div class="content">
    <div class="content-top">
      <div class="progress">
        <canvas class="canvas" id="canvas"></canvas>
        <div class="cover-box">
          <image
            if="{{!isDark }}"
            class="fixed-image"
            src="../../images/subtract-light.svg"
          ></image>
          <image
            else
            class="fixed-image"
            src="../../images/subtract-dark.svg"
          ></image>
        </div>
      </div>
    </div>
    <div class="null"></div>
    <text class="{{ 'type-text ' + textColor }}">{{
      type === 'text'
        ? $t('message.generateing.transfering')
        : $t('message.generateing.summary_create_dian_new')
    }}</text>
    <text class="{{ 'content-text ' + contentColor }}">{{ contentText }}</text>
  </div>
</template>

<script>
import { formatTime } from '../../common/utils.js'
let ctx = null
let colorArr = []
export default {
  props: {
    isDark: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: 'abstract',
    },
    percent: Number,
    title: {
      type: String,
      default: '',
    },
    statusCode: Number,
  },
  data() {
    return {
      drawComplete: false,
      lastPercent: 0,
    }
  },
  onInit() {
    // 监听数据变化
    this.$watch('percent', 'watchPropsChange')
  },
  watchPropsChange(newV, oldV) {
    console.log('监测到进度条发生变化', newV, oldV)
    this.drawProgress()
    if (this.percent === 100 && [2003, 2004].includes(this.statusCode)) {
      this.$emit('changeStatus', 'done')
    }
  },

  computed: {
    contentText() {
      return this.title
    },
    backgroundImg() {
      if (this.type === 'text') {
        return ''
      }
      if (this.isDark) {
        return 'dark'
      }
      return 'light'
    },
    textColor() {
      if (this.isDark) {
        return 'text-dark'
      }
      return 'text-light'
    },
    contentColor() {
      if (this.isDark) {
        return 'content-dark'
      }
      return 'content-light'
    },
  },
  onReady() {
    if (!this.drawComplete) {
      let timer = setTimeout(() => {
        this.drawCanvas()
        clearTimeout(timer)
      }, 0)
    }
  },
  // 绘制轨道
  drawTrack() {
    ctx.clearRect(0, 0, 40, 40)
    ctx.save()
    ctx.beginPath()
    ctx.lineCap = 'round'
    ctx.lineWidth = 3
    ctx.strokeStyle = this.isDark ? '#525252' : '#e3e3e4'
    ctx.arc(20, 20, 18, 0, 2 * Math.PI)
    ctx.stroke()
    ctx.closePath()
    ctx.restore()
  },
  drawCanvas() {
    const canvas = this.$element('canvas') //获取 canvas 组件
    if (canvas) {
      ctx = canvas.getContext('2d')
      this.drawTrack()
      this.getColors()
      this.drawProgress()
    }
  },
  getColors() {
    let startColor = '#10bff7'
    let endColor = '#29e549'
    if (this.type === 'text') {
      startColor = '#00bd13'
      endColor = '#00bd13'
    }

    const percent = 100
    function getColorArr() {
      let startRgb = hexToRgb(startColor)
      let midRgb1 = hexToRgb('#4a86ff')
      let midRgb2 = hexToRgb('#c25dff')
      let midRgb3 = hexToRgb('#ff6536')
      let midRgb4 = hexToRgb('#ffc219')
      let endRgb = hexToRgb(endColor)

      let stepR1 = (midRgb1[0] - startRgb[0]) / (percent / 5)
      let stepG1 = (midRgb1[1] - startRgb[1]) / (percent / 5)
      let stepB1 = (midRgb1[2] - startRgb[2]) / (percent / 5)

      let stepR2 = (midRgb2[0] - midRgb1[0]) / (percent / 5)
      let stepG2 = (midRgb2[1] - midRgb1[1]) / (percent / 5)
      let stepB2 = (midRgb2[2] - midRgb1[2]) / (percent / 5)

      let stepR3 = (midRgb3[0] - midRgb2[0]) / (percent / 5)
      let stepG3 = (midRgb3[1] - midRgb2[1]) / (percent / 5)
      let stepB3 = (midRgb3[2] - midRgb2[2]) / (percent / 5)

      let stepR4 = (midRgb4[0] - midRgb3[0]) / (percent / 5)
      let stepG4 = (midRgb4[1] - midRgb3[1]) / (percent / 5)
      let stepB4 = (midRgb4[2] - midRgb3[2]) / (percent / 5)

      let stepR5 = (endRgb[0] - midRgb4[0]) / (percent / 5)
      let stepG5 = (endRgb[1] - midRgb4[1]) / (percent / 5)
      let stepB5 = (endRgb[2] - midRgb4[2]) / (percent / 5)

      for (let i = 0; i < percent / 5; i++) {
        let hex = rgbToHex(
          startRgb[0] + stepR1 * i,
          startRgb[1] + stepG1 * i,
          startRgb[2] + stepB1 * i
        )
        hex = hex.length == 4 ? '00' + hex : hex.length == 5 ? '0' + hex : hex
        colorArr.push('#' + hex)
      }

      for (let i = percent / 5; i < (percent * 2) / 5; i++) {
        let hex = rgbToHex(
          midRgb1[0] + stepR2 * (i - percent / 5),
          midRgb1[1] + stepG2 * (i - percent / 5),
          midRgb1[2] + stepB2 * (i - percent / 5)
        )
        hex = hex.length == 4 ? '00' + hex : hex.length == 5 ? '0' + hex : hex
        colorArr.push('#' + hex)
      }

      for (let i = (percent * 2) / 5; i < (percent * 3) / 5; i++) {
        let hex = rgbToHex(
          midRgb2[0] + stepR3 * (i - (percent * 2) / 5),
          midRgb2[1] + stepG3 * (i - (percent * 2) / 5),
          midRgb2[2] + stepB3 * (i - (percent * 2) / 5)
        )
        hex = hex.length == 4 ? '00' + hex : hex.length == 5 ? '0' + hex : hex
        colorArr.push('#' + hex)
      }

      for (let i = (percent * 3) / 5; i < (percent * 4) / 5; i++) {
        let hex = rgbToHex(
          midRgb3[0] + stepR4 * (i - (percent * 3) / 5),
          midRgb3[1] + stepG4 * (i - (percent * 3) / 5),
          midRgb3[2] + stepB4 * (i - (percent * 3) / 5)
        )
        hex = hex.length == 4 ? '00' + hex : hex.length == 5 ? '0' + hex : hex
        colorArr.push('#' + hex)
      }

      for (let i = (percent * 4) / 5; i < percent; i++) {
        let hex = rgbToHex(
          midRgb4[0] + stepR5 * (i - (percent * 4) / 5),
          midRgb4[1] + stepG5 * (i - (percent * 4) / 5),
          midRgb4[2] + stepB5 * (i - (percent * 4) / 5)
        )
        hex = hex.length == 4 ? '00' + hex : hex.length == 5 ? '0' + hex : hex
        colorArr.push('#' + hex)
      }
    }
    if (this.type === 'abstract') {
      getColorArr()
    }
    function hexToRgb(hex) {
      if (hex.length == 4) {
        return [
          parseInt('0x' + hex.slice(1, 2).repeat(2)),
          parseInt('0x' + hex.slice(2, 3).repeat(2)),
          parseInt('0x' + hex.slice(3, 4).repeat(2)),
        ]
      } else if (hex.length == 7) {
        return [
          parseInt('0x' + hex.slice(1, 3)),
          parseInt('0x' + hex.slice(3, 5)),
          parseInt('0x' + hex.slice(5, 7)),
        ]
      } else {
      }
    }

    function rgbToHex(r, g, b) {
      return ((r << 16) | (g << 8) | b).toString(16)
    }
  },
  drawProgress() {
    const type = this.type
    let percent = this.percent
    let num = this.lastPercent

    //绘制进度环
    function drawProgress() {
      ctx.save()
      ctx.beginPath()
      ctx.lineCap = 'round'
      ctx.lineWidth = 3
      ctx.strokeStyle = type === 'text' ? '#00bd13' : colorArr[num]
      ctx.arc(
        20,
        20,
        18,
        -Math.PI / 2 + ((2 * (num - 1)) / 100) * Math.PI,
        -Math.PI / 2 + ((2 * num) / 100) * Math.PI
      )
      ctx.stroke()
      ctx.closePath()
      ctx.restore()
      console.log('开始执行drawProgress,进度：', percent)
    }
    // 动画
    function draw() {
      ctx.clearRect(85, 85, 130, 130)
      drawProgress()
    }
    let timer = setInterval(() => {
      num += 1
      if (num <= percent) {
        draw()
      } else {
        this.lastPercent = percent
        clearInterval(timer)
      }
    }, 16)
  },
}
</script>

<style lang="less">
.content {
  height: 100%;
  width: 100%;
  padding: 16dp;
  display: flex;
  align-content: center;
  justify-content: center;
  flex-direction: column;
  .content-top {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    .progress {
      width: 40px;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-content: center;
      justify-content: center;
      background-repeat: no-repeat;
      background-size: 15px;
      background-position: center;
      position: relative;
      .canvas {
        width: 40px;
        height: 40px;
        align-self: center;
        background-color: transparent;
      }
      .cover-box {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0px;
        top: 0px;
        display: flex;
        align-items: center;
        justify-content: center;
        .fixed-image {
          width: 16px;
          height: 19px;
        }
      }
    }
  }
  .null {
    width: 100%;
    height: 5px;
  }

  .type-text {
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
    text-align: center;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    lines: 2;
    text-overflow: ellipsis;
    max-height: 22px;
  }
  .text-dark {
    color: #ffffff;
  }
  .text-light {
    color: #000000;
  }
  .content-text {
    /* margin-top: 2px; */
    font-size: 12px;
    font-weight: 500;
    max-height: 40px;
    line-height: 16px;
    text-align: center;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    lines: 2;
    text-overflow: ellipsis;
  }
  .content-dark {
    color: #a1a1a1;
  }
  .content-light {
    color: #717172;
  }
}
</style>
